/**
 * API Client for PulseChain News Hub
 * Handles all frontend-backend communication
 */

class PulseChainAPI {
    constructor() {
        this.baseURL = window.location.origin;
        this.apiURL = `${this.baseURL}/api`;
        this.wsURL = `ws://${window.location.hostname}:3002`;
        this.websocket = null;
        this.listeners = new Map();
        
        // Initialize WebSocket connection
        this.connectWebSocket();
    }

    // ========== HTTP API METHODS ==========

    /**
     * Generic API request method
     */
    async request(endpoint, options = {}) {
        try {
            const url = endpoint.startsWith('http') ? endpoint : `${this.apiURL}${endpoint}`;
            
            const response = await fetch(url, {
                headers: {
                    'Content-Type': 'application/json',
                    ...options.headers
                },
                ...options
            });

            if (!response.ok) {
                throw new Error(`API Error: ${response.status} ${response.statusText}`);
            }

            const data = await response.json();
            return data;

        } catch (error) {
            console.error('API Request Error:', error);
            throw error;
        }
    }

    /**
     * Get API health status
     */
    async getHealth() {
        return this.request('/health');
    }

    /**
     * Get recent articles with filtering
     */
    async getArticles(params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const endpoint = queryString ? `/articles?${queryString}` : '/articles';
        return this.request(endpoint);
    }

    /**
     * Get specific article by ID
     */
    async getArticle(id) {
        return this.request(`/articles/${id}`);
    }

    /**
     * Search articles with query and filters
     */
    async searchArticles(params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const endpoint = queryString ? `/articles/search?${queryString}` : '/articles/search';
        return this.request(endpoint);
    }

    /**
     * Get articles by sentiment
     */
    async getArticlesBySentiment(sentiment) {
        return this.request(`/articles/sentiment/${sentiment}`);
    }

    /**
     * Get current DEX data
     */
    async getDexData() {
        return this.request('/dex/current');
    }

    /**
     * Get enhanced DEX data with AI analysis
     */
    async getEnhancedDexData() {
        return this.request('/dex/enhanced');
    }

    /**
     * Get price history for a token
     */
    async getPriceHistory(token, hours = 24) {
        return this.request(`/dex/history/${token}?hours=${hours}`);
    }

    /**
     * Get active trading signals
     */
    async getTradingSignals() {
        return this.request('/signals');
    }

    /**
     * Update trading signal performance
     */
    async updateSignalPerformance(signalId, outcome, performanceScore) {
        return this.request(`/signals/${signalId}/performance`, {
            method: 'POST',
            body: JSON.stringify({ outcome, performanceScore })
        });
    }

    /**
     * Get news sources
     */
    async getSources() {
        return this.request('/sources');
    }

    /**
     * Get analytics data
     */
    async getAnalytics(category = null, hours = 24) {
        const params = new URLSearchParams({ hours });
        if (category) params.append('category', category);
        return this.request(`/analytics?${params.toString()}`);
    }

    /**
     * Get dashboard data (combined endpoint)
     */
    async getDashboardData() {
        return this.request('/dashboard');
    }

    /**
     * Trigger manual analysis
     */
    async triggerAnalysis() {
        return this.request('/analysis/run', { method: 'POST' });
    }

    /**
     * Get analysis pipeline status
     */
    async getAnalysisStatus() {
        return this.request('/analysis/status');
    }

    // ========== WEBSOCKET METHODS ==========

    /**
     * Connect to WebSocket for real-time updates
     */
    connectWebSocket() {
        try {
            this.websocket = new WebSocket(this.wsURL);

            this.websocket.onopen = () => {
                console.log('WebSocket connected');
                this.send({ type: 'subscribe' });
                this.notifyListeners('ws_connected', { connected: true });
            };

            this.websocket.onmessage = (event) => {
                try {
                    const data = JSON.parse(event.data);
                    console.log('WebSocket message:', data);
                    this.handleWebSocketMessage(data);
                } catch (error) {
                    console.error('WebSocket message parse error:', error);
                }
            };

            this.websocket.onclose = () => {
                console.log('WebSocket disconnected');
                this.notifyListeners('ws_disconnected', { connected: false });
                
                // Attempt reconnection after 5 seconds
                setTimeout(() => this.connectWebSocket(), 5000);
            };

            this.websocket.onerror = (error) => {
                console.error('WebSocket error:', error);
                this.notifyListeners('ws_error', { error });
            };

        } catch (error) {
            console.error('WebSocket connection error:', error);
        }
    }

    /**
     * Handle incoming WebSocket messages
     */
    handleWebSocketMessage(data) {
        switch (data.type) {
            case 'connection':
                console.log('WebSocket connection confirmed:', data.message);
                break;
                
            case 'scheduled_update':
                this.notifyListeners('data_update', data.data);
                break;
                
            case 'analysis_complete':
                this.notifyListeners('analysis_complete', data.data);
                break;
                
            case 'new_article':
                this.notifyListeners('new_article', data.data);
                break;
                
            case 'price_alert':
                this.notifyListeners('price_alert', data.data);
                break;
                
            default:
                console.log('Unknown WebSocket message type:', data.type);
        }
    }

    /**
     * Send message via WebSocket
     */
    send(data) {
        if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
            this.websocket.send(JSON.stringify(data));
        }
    }

    /**
     * Subscribe to WebSocket events
     */
    addEventListener(event, callback) {
        if (!this.listeners.has(event)) {
            this.listeners.set(event, []);
        }
        this.listeners.get(event).push(callback);
    }

    /**
     * Unsubscribe from WebSocket events
     */
    removeEventListener(event, callback) {
        if (this.listeners.has(event)) {
            const callbacks = this.listeners.get(event);
            const index = callbacks.indexOf(callback);
            if (index > -1) {
                callbacks.splice(index, 1);
            }
        }
    }

    /**
     * Notify all listeners of an event
     */
    notifyListeners(event, data) {
        if (this.listeners.has(event)) {
            this.listeners.get(event).forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error(`Event listener error for ${event}:`, error);
                }
            });
        }
    }

    // ========== UTILITY METHODS ==========

    /**
     * Format price with appropriate precision
     */
    formatPrice(price) {
        if (price < 0.001) {
            return price.toFixed(8);
        } else if (price < 1) {
            return price.toFixed(4);
        } else {
            return price.toFixed(2);
        }
    }

    /**
     * Format percentage change
     */
    formatPercentage(change) {
        const sign = change >= 0 ? '+' : '';
        return `${sign}${change.toFixed(2)}%`;
    }

    /**
     * Get color class for price change
     */
    getPriceChangeColor(change) {
        if (change > 0) return 'text-green-600';
        if (change < 0) return 'text-red-600';
        return 'text-gray-600';
    }

    /**
     * Format timestamp to relative time
     */
    formatRelativeTime(timestamp) {
        const now = new Date();
        const time = new Date(timestamp);
        const diffInSeconds = Math.floor((now - time) / 1000);

        if (diffInSeconds < 60) {
            return `${diffInSeconds}s ago`;
        } else if (diffInSeconds < 3600) {
            return `${Math.floor(diffInSeconds / 60)}m ago`;
        } else if (diffInSeconds < 86400) {
            return `${Math.floor(diffInSeconds / 3600)}h ago`;
        } else {
            return `${Math.floor(diffInSeconds / 86400)}d ago`;
        }
    }

    /**
     * Get sentiment color and icon
     */
    getSentimentDisplay(sentiment) {
        const displays = {
            positive: {
                color: 'text-green-600 bg-green-100',
                icon: '📈',
                label: 'Positive'
            },
            negative: {
                color: 'text-red-600 bg-red-100',
                icon: '📉',
                label: 'Negative'
            },
            neutral: {
                color: 'text-gray-600 bg-gray-100',
                icon: '➖',
                label: 'Neutral'
            }
        };
        
        return displays[sentiment] || displays.neutral;
    }

    /**
     * Get trading signal display
     */
    getSignalDisplay(signal) {
        const displays = {
            BUY: {
                color: 'text-green-600 bg-green-100',
                icon: '🟢',
                label: 'BUY'
            },
            SELL: {
                color: 'text-red-600 bg-red-100',
                icon: '🔴',
                label: 'SELL'
            },
            HOLD: {
                color: 'text-yellow-600 bg-yellow-100',
                icon: '🟡',
                label: 'HOLD'
            }
        };
        
        return displays[signal] || displays.HOLD;
    }

    /**
     * Show notification (if supported)
     */
    showNotification(title, body, options = {}) {
        if ('Notification' in window && Notification.permission === 'granted') {
            return new Notification(title, { body, ...options });
        } else if ('Notification' in window && Notification.permission !== 'denied') {
            Notification.requestPermission().then(permission => {
                if (permission === 'granted') {
                    return new Notification(title, { body, ...options });
                }
            });
        }
    }

    /**
     * Cache management
     */
    setCache(key, data, ttl = 300000) { // 5 minutes default
        const item = {
            data,
            timestamp: Date.now(),
            ttl
        };
        localStorage.setItem(`pulsechain_${key}`, JSON.stringify(item));
    }

    getCache(key) {
        try {
            const item = JSON.parse(localStorage.getItem(`pulsechain_${key}`));
            if (item && Date.now() - item.timestamp < item.ttl) {
                return item.data;
            }
        } catch (error) {
            console.error('Cache retrieval error:', error);
        }
        return null;
    }

    clearCache() {
        Object.keys(localStorage).forEach(key => {
            if (key.startsWith('pulsechain_')) {
                localStorage.removeItem(key);
            }
        });
    }
}

// Create global API instance
window.PulseChainAPI = new PulseChainAPI();

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PulseChainAPI;
}