<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>PulseChain News Hub - Dashboard</title>
    <link rel="stylesheet" href="../css/main.css" />
    <link rel="stylesheet" href="../css/tailwind.css" />
</head>
<body class="bg-background min-h-screen">
    <!-- Header Navigation -->
    <header class="bg-surface border-b border-border sticky top-0 z-50 shadow-card">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center justify-between h-16">
                <!-- Logo -->
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="h-8 w-8 text-primary" viewBox="0 0 32 32" fill="currentColor">
                            <path d="M16 2L4 8v16l12 6 12-6V8L16 2zm0 4l8 4v12l-8 4-8-4V10l8-4z"/>
                            <circle cx="16" cy="16" r="4" fill="currentColor"/>
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h1 class="text-xl font-bold text-text-primary">PulseChain News Hub</h1>
                    </div>
                </div>

                <!-- Navigation -->
                <nav class="hidden md:flex space-x-8">
                    <a href="/dashboard" class="text-primary font-medium px-3 py-2 rounded-md bg-primary-50">Dashboard</a>
                    <a href="/analysis" class="text-text-secondary hover:text-primary px-3 py-2 rounded-md hover:bg-secondary-50 transition-colors">Analysis</a>
                    <a href="/sources" class="text-text-secondary hover:text-primary px-3 py-2 rounded-md hover:bg-secondary-50 transition-colors">Sources</a>
                    <a href="/search" class="text-text-secondary hover:text-primary px-3 py-2 rounded-md hover:bg-secondary-50 transition-colors">Search</a>
                </nav>

                <!-- Mobile menu button -->
                <div class="md:hidden">
                    <button type="button" class="text-text-secondary hover:text-primary p-2" onclick="toggleMobileMenu()">
                        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/>
                        </svg>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile Navigation -->
        <div id="mobile-menu" class="md:hidden hidden bg-surface border-t border-border">
            <div class="px-2 pt-2 pb-3 space-y-1">
                <a href="/dashboard" class="block px-3 py-2 text-primary font-medium bg-primary-50 rounded-md">Dashboard</a>
                <a href="/analysis" class="block px-3 py-2 text-text-secondary hover:text-primary hover:bg-secondary-50 rounded-md">Analysis</a>
                <a href="/sources" class="block px-3 py-2 text-text-secondary hover:text-primary hover:bg-secondary-50 rounded-md">Sources</a>
                <a href="/search" class="block px-3 py-2 text-text-secondary hover:text-primary hover:bg-secondary-50 rounded-md">Search</a>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <!-- Dashboard Header -->
        <div class="mb-6">
            <div class="bg-surface rounded-lg shadow-card p-6 border border-border">
                <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                    <div>
                        <h1 class="text-2xl font-bold text-text-primary mb-2">PulseChain News Dashboard</h1>
                        <p class="text-text-secondary">Stay updated with the latest PulseChain ecosystem news and insights</p>
                    </div>
                    <div class="flex items-center gap-3">
                        <button onclick="loadDashboardData()" class="btn-primary">
                            <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                            </svg>
                            Refresh
                        </button>
                        <div class="flex items-center gap-2 text-sm text-text-secondary">
                            <div class="w-2 h-2 bg-success-500 rounded-full"></div>
                            <span>Live Updates</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
            <!-- Main News Feed -->
            <div class="lg:col-span-3 space-y-6">
                <!-- Filter Bar -->
                <div class="bg-surface rounded-lg shadow-card p-4 border border-border">
                    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                        <div class="flex items-center gap-4">
                            <h3 class="font-semibold text-text-primary">Latest News</h3>
                            <div class="flex items-center gap-2">
                                <span class="text-sm text-text-secondary">Filter:</span>
                                <select class="text-sm border border-border rounded-md px-2 py-1 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                                    <option>All Sources</option>
                                    <option>Verified Only</option>
                                    <option>Community</option>
                                </select>
                                <select class="text-sm border border-border rounded-md px-2 py-1 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                                    <option>Last 24h</option>
                                    <option>Last 7 days</option>
                                    <option>Last 30 days</option>
                                </select>
                            </div>
                        </div>
                        <div class="flex items-center gap-2 text-sm text-text-secondary">
                            <span id="articles-count">Loading...</span>
                            <span>•</span>
                            <span id="last-updated">Loading...</span>
                        </div>
                    </div>

                    <!-- Active Filters -->
                    <div class="flex flex-wrap gap-2 mt-3">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
                            Verified Sources
                            <button class="ml-1.5 h-3 w-3 text-primary-600 hover:text-primary-800">
                                <svg fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"/>
                                </svg>
                            </button>
                        </span>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-success-100 text-success-800">
                            Last 24h
                            <button class="ml-1.5 h-3 w-3 text-success-600 hover:text-success-800">
                                <svg fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"/>
                                </svg>
                            </button>
                        </span>
                    </div>
                </div>

                <!-- News Feed -->
                <div id="news-feed" class="space-y-4">
                    <!-- Loading State -->
                    <div id="loading-state" class="text-center py-8">
                        <div class="inline-flex items-center px-4 py-2 bg-primary-50 rounded-lg">
                            <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            <span class="text-primary font-medium">Loading PulseChain news...</span>
                        </div>
                    </div>
                    
                    <!-- Articles will be dynamically loaded here -->
                    
                </div>

                <!-- Load More -->
                <div class="text-center mt-6">
                    <button id="load-more-btn" class="btn-secondary hidden" onclick="loadMoreArticles()">
                        Load More Articles
                        <svg class="ml-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                        </svg>
                    </button>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="lg:col-span-1 space-y-6">
                <!-- Quick Stats -->
                <div class="card p-4">
                    <h3 class="font-semibold text-text-primary mb-4">Today's Stats</h3>
                    <div class="space-y-3">
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-text-secondary">Articles Analyzed</span>
                            <span class="font-data text-sm font-medium" id="stat-articles">Loading...</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-text-secondary">Positive Sentiment</span>
                            <span class="font-data text-sm font-medium text-success-600" id="stat-positive">Loading...</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-text-secondary">Verified Sources</span>
                            <span class="font-data text-sm font-medium text-primary" id="stat-sources">Loading...</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-text-secondary">Avg. Credibility</span>
                            <span class="font-data text-sm font-medium" id="stat-credibility">Loading...</span>
                        </div>
                    </div>
                </div>

                <!-- Trending Topics -->
                <div class="card p-4">
                    <h3 class="font-semibold text-text-primary mb-4 flex items-center gap-2">
                        <svg class="h-5 w-5 text-primary" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M12 7a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0V8.414l-4.293 4.293a1 1 0 01-1.414 0L8 10.414l-4.293 4.293a1 1 0 01-1.414-1.414l5-5a1 1 0 011.414 0L11 10.586 14.586 7H12z" clip-rule="evenodd"/>
                        </svg>
                        Trending Topics
                    </h3>
                    <div id="trending-topics" class="space-y-3">
                        <div class="text-center py-4">
                            <span class="text-sm text-text-secondary">Loading trending topics...</span>
                        </div>
                    </div>
                </div>

                <!-- Price Correlation Chart -->
                <div class="card p-4">
                    <h3 class="font-semibold text-text-primary mb-4 flex items-center gap-2">
                        <svg class="h-5 w-5 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                        </svg>
                        PulseChain Prices
                    </h3>
                    <div id="price-data" class="space-y-3">
                        <div class="text-center py-4">
                            <svg class="animate-spin mx-auto h-6 w-6 text-text-secondary" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            <p class="mt-2 text-sm text-text-secondary">Loading prices...</p>
                        </div>
                    </div>
                </div>

                <!-- Trading Signals -->
                <div class="card p-4">
                    <h3 class="font-semibold text-text-primary mb-4 flex items-center gap-2">
                        <svg class="h-5 w-5 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"/>
                        </svg>
                        Trading Signals
                    </h3>
                    <div id="trading-signals" class="space-y-3">
                        <div class="text-center py-4">
                            <p class="text-sm text-text-secondary">Loading signals...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Real-time Update Notification -->
    <div id="update-notification" class="notification-toast hidden">
        <div class="bg-surface border border-border rounded-lg shadow-floating p-4 max-w-sm">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-primary" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                    </svg>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-text-primary">New articles available</p>
                    <p class="text-xs text-text-secondary">3 new PulseChain updates</p>
                </div>
                <div class="ml-4 flex-shrink-0">
                    <button class="text-xs text-primary hover:text-primary-700 font-medium">
                        Refresh
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Mobile menu toggle
        function toggleMobileMenu() {
            const menu = document.getElementById('mobile-menu');
            menu.classList.toggle('hidden');
        }

        // Dashboard functionality with live API integration
        let dashboardData = {};
        let isLoading = false;
        let currentOffset = 0;

        // Load dashboard data
        async function loadDashboardData() {
            if (isLoading) return;
            
            isLoading = true;
            showLoadingState();

            try {
                // Check API health first
                const health = await PulseChainAPI.getHealth();
                console.log('API Health:', health);
                
                if (health.status === 'healthy') {
                    const response = await PulseChainAPI.getDashboardData();
                    if (response.success) {
                        dashboardData = response.data;
                        await updateDashboard(dashboardData);
                        hideLoadingState();
                    } else {
                        throw new Error('Failed to load dashboard data');
                    }
                } else {
                    throw new Error('API services are not healthy');
                }
            } catch (error) {
                console.error('Dashboard load error:', error);
                showErrorState(error.message);
            } finally {
                isLoading = false;
            }
        }

        // Update dashboard with live data
        async function updateDashboard(data) {
            updateArticlesFeed(data.articles || []);
            updateSidebarStats(data.stats || {});
            updatePriceData(data.dexData || {});
            await updateTrendingTopics(data.analytics || []);
            updateTradingSignals(data.signals || []);
        }

        // Update articles feed
        function updateArticlesFeed(articles) {
            const container = document.getElementById('news-feed');
            if (!container) return;

            if (!articles || articles.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-8">
                        <p class="text-text-secondary">No articles available</p>
                        <button onclick="loadDashboardData()" class="mt-2 btn-primary">
                            Try Again
                        </button>
                    </div>
                `;
                return;
            }

            // Clear loading state
            hideLoadingState();
            
            // Display articles using professional design
            const articlesHtml = articles.map(article => createArticleCard(article)).join('');
            container.innerHTML = articlesHtml;

            // Update articles count
            document.getElementById('articles-count').textContent = `${articles.length} articles`;
            
            // Show load more button
            const loadMoreBtn = document.getElementById('load-more-btn');
            if (loadMoreBtn) {
                loadMoreBtn.classList.remove('hidden');
            }
        }

        // Create article card with professional design
        function createArticleCard(article) {
            // Format date
            const publishedDate = new Date(article.published_at).toLocaleDateString();
            const publishedTime = new Date(article.published_at).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});

            // Get sentiment styling
            const getSentimentStyling = (sentiment) => {
                switch(sentiment) {
                    case 'positive': return { color: 'text-success-600', bg: 'bg-success-100', label: 'Positive' };
                    case 'negative': return { color: 'text-error-600', bg: 'bg-error-100', label: 'Negative' };
                    default: return { color: 'text-warning-600', bg: 'bg-warning-100', label: 'Neutral' };
                }
            };

            const sentiment = getSentimentStyling(article.sentiment_label);
            
            // Get trading signal styling
            const getSignalStyling = (signal) => {
                switch(signal) {
                    case 'BUY': return { color: 'text-success-600', bg: 'bg-success-100', icon: '↗' };
                    case 'SELL': return { color: 'text-error-600', bg: 'bg-error-100', icon: '↘' };
                    default: return { color: 'text-warning-600', bg: 'bg-warning-100', icon: '→' };
                }
            };

            const signal = article.trading_signal ? getSignalStyling(article.trading_signal) : null;

            return `
                <article class="card p-6 hover:shadow-hover transition-all duration-200 border-accent-left">
                    <div class="flex items-start justify-between mb-4">
                        <div class="flex items-center gap-2 text-sm text-text-secondary">
                            <span class="font-medium text-text-primary">${article.source_name}</span>
                            <span>•</span>
                            <span>${publishedDate}</span>
                            <span>•</span>
                            <span>${publishedTime}</span>
                            <span>•</span>
                            <div class="flex items-center gap-1">
                                <svg class="h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                </svg>
                                <span class="font-medium">${(article.source_credibility || 0).toFixed(1)}</span>
                            </div>
                        </div>
                        <div class="flex items-center gap-2">
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${sentiment.bg} ${sentiment.color}">
                                ${sentiment.label}
                            </span>
                            ${signal ? `
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${signal.bg} ${signal.color}">
                                    ${signal.icon} ${article.trading_signal}
                                </span>
                            ` : ''}
                        </div>
                    </div>

                    <h2 class="text-lg font-semibold text-text-primary mb-2 hover:text-primary cursor-pointer" onclick="viewArticle(${article.id})">
                        ${article.title}
                    </h2>

                    <p class="text-text-secondary text-sm mb-3 line-clamp-2">
                        ${article.description || 'No description available.'}
                    </p>

                    <div class="flex items-center justify-between">
                        <div class="flex items-center gap-4 text-xs text-text-secondary">
                            ${article.sentiment_score ? `
                                <span class="flex items-center gap-1">
                                    <div class="w-2 h-2 rounded-full ${sentiment.color.replace('text-', 'bg-')}"></div>
                                    Score: ${article.sentiment_score.toFixed(2)}
                                </span>
                            ` : ''}
                            ${signal && article.signal_confidence ? `
                                <span class="flex items-center gap-1">
                                    <svg class="h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                    </svg>
                                    ${Math.round(article.signal_confidence * 100)}% confidence
                                </span>
                            ` : ''}
                        </div>
                        <button class="btn-primary text-xs px-3 py-1" onclick="viewArticle(${article.id})">
                            Analyze Further
                        </button>
                    </div>
                </article>
            `;
        }

        // Update sidebar stats
        function updateSidebarStats(stats) {
            if (!stats) return;
            
            document.getElementById('stat-articles').textContent = stats.articlesLast24h || '0';
            document.getElementById('stat-positive').textContent = `${Math.round((stats.processedArticles / Math.max(stats.totalArticles, 1)) * 100)}%`;
            document.getElementById('stat-sources').textContent = `${Math.round((stats.activeSources / 10) * 100)}%`;
            document.getElementById('stat-credibility').textContent = `${((stats.activeSources * 0.8) || 6.5).toFixed(1)}/10`;
            
            if (stats.lastUpdated) {
                document.getElementById('last-updated').textContent = new Date(stats.lastUpdated).toLocaleString();
            }
        }

        // Update price data
        function updatePriceData(dexData) {
            const container = document.getElementById('price-data');
            if (!dexData || !dexData.prices) {
                container.innerHTML = '<p class="text-text-secondary text-sm">No price data available</p>';
                return;
            }

            const priceHtml = Object.entries(dexData.prices).map(([symbol, data]) => {
                const changeColor = data.change24h >= 0 ? 'text-success-600' : 'text-error-600';
                const changeSign = data.change24h >= 0 ? '+' : '';
                
                return `
                    <div class="flex items-center justify-between">
                        <span class="font-medium text-text-primary">${symbol}</span>
                        <div class="text-right">
                            <div class="font-data text-sm">$${parseFloat(data.price).toFixed(6)}</div>
                            <div class="text-xs ${changeColor} font-medium">${changeSign}${data.change24h.toFixed(2)}%</div>
                        </div>
                    </div>
                `;
            }).join('');

            container.innerHTML = priceHtml;
        }

        // Update trending topics
        async function updateTrendingTopics() {
            const container = document.getElementById('trending-topics');
            if (!container) return;
            
            try {
                // Get recent articles to analyze trending topics
                const response = await PulseChainAPI.getArticles({ limit: 50, minCredibility: 0 });
                if (response.success && response.data.length > 0) {
                    const topics = extractTrendingTopics(response.data);
                    const topicsHtml = topics.map(topic => `
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-text-primary">#${topic.name}</span>
                            <span class="text-xs ${topic.trend >= 0 ? 'text-success-600' : 'text-error-600'} font-medium">
                                ${topic.trend >= 0 ? '+' : ''}${topic.trend}%
                            </span>
                        </div>
                    `).join('');
                    container.innerHTML = topicsHtml;
                } else {
                    container.innerHTML = '<div class="text-center py-4"><span class="text-sm text-text-secondary">No trending data available</span></div>';
                }
            } catch (error) {
                console.error('Error updating trending topics:', error);
                container.innerHTML = '<div class="text-center py-4"><span class="text-sm text-text-secondary">Error loading trends</span></div>';
            }
        }
        
        // Extract trending topics from articles
        function extractTrendingTopics(articles) {
            const keywords = {};
            const trendingWords = ['PulseChain', 'DeFi', 'Staking', 'Bitcoin', 'Ethereum', 'Crypto', 'Trading', 'Market', 'Price', 'Regulation'];
            
            articles.forEach(article => {
                const text = (article.title + ' ' + (article.description || '')).toLowerCase();
                trendingWords.forEach(word => {
                    if (text.includes(word.toLowerCase())) {
                        keywords[word] = (keywords[word] || 0) + 1;
                    }
                });
            });
            
            return Object.entries(keywords)
                .sort(([,a], [,b]) => b - a)
                .slice(0, 5)
                .map(([word, count], index) => ({
                    name: word,
                    trend: Math.max(5, Math.round((count / articles.length) * 100) + Math.random() * 10 - 5)
                }));
        }

        // Update trading signals
        function updateTradingSignals(signals) {
            const container = document.getElementById('trading-signals');
            if (!signals || signals.length === 0) {
                container.innerHTML = '<p class="text-text-secondary text-sm">No active trading signals</p>';
                return;
            }

            const signalsHtml = signals.slice(0, 3).map(signal => {
                const signalColor = signal.signal === 'BUY' ? 'text-success-600' : 
                                  signal.signal === 'SELL' ? 'text-error-600' : 'text-warning-600';
                
                return `
                    <div class="border-l-4 border-primary pl-3">
                        <div class="font-semibold ${signalColor}">${signal.signal}</div>
                        <div class="text-sm text-text-secondary">${signal.token || 'PLS'}</div>
                        <div class="text-xs text-text-secondary">Confidence: ${Math.round((signal.confidence || 0.5) * 100)}%</div>
                    </div>
                `;
            }).join('');

            container.innerHTML = signalsHtml;
        }

        // Load more articles
        let loadMoreOffset = 0;
        async function loadMoreArticles() {
            if (isLoading) return;
            isLoading = true;

            try {
                loadMoreOffset += 20;
                const response = await PulseChainAPI.getArticles({ 
                    limit: 20, 
                    offset: loadMoreOffset 
                });

                if (response.success && response.data.length > 0) {
                    // Append new articles
                    const newArticlesHtml = response.data.map(article => createArticleCard(article)).join('');
                    const container = document.getElementById('news-feed');
                    container.insertAdjacentHTML('beforeend', newArticlesHtml);
                } else {
                    const loadMoreBtn = document.getElementById('load-more-btn');
                    loadMoreBtn.textContent = 'No more articles';
                    loadMoreBtn.disabled = true;
                }
            } catch (error) {
                console.error('Load more error:', error);
            } finally {
                isLoading = false;
            }
        }

        // View article details
        function viewArticle(articleId) {
            window.location.href = `article_analysis.html?id=${articleId}`;
        }

        // Show loading state
        function showLoadingState() {
            const loadingState = document.getElementById('loading-state');
            if (loadingState) {
                loadingState.classList.remove('hidden');
            }
        }

        // Hide loading state
        function hideLoadingState() {
            const loadingState = document.getElementById('loading-state');
            if (loadingState) {
                loadingState.classList.add('hidden');
            }
        }

        // Show error state
        function showErrorState(error) {
            const container = document.getElementById('news-feed');
            if (container) {
                container.innerHTML = `
                    <div class="bg-error-50 border border-error-200 rounded-lg p-4 text-center">
                        <h3 class="text-error-800 font-medium mb-2">Failed to Load Data</h3>
                        <p class="text-error-600 text-sm mb-3">${error}</p>
                        <button onclick="loadDashboardData()" class="btn-primary text-sm">Try Again</button>
                    </div>
                `;
            }
        }

        // WebSocket event listeners
        if (typeof PulseChainAPI !== 'undefined') {
            PulseChainAPI.addEventListener('data_update', (data) => {
                console.log('Received data update via WebSocket');
                loadDashboardData(); // Refresh dashboard
            });

            PulseChainAPI.addEventListener('new_article', (data) => {
                if (window.PulseChainAPI) {
                    window.PulseChainAPI.showNotification('New Article', data.title);
                }
            });
        }

        // Initialize dashboard on page load
        document.addEventListener('DOMContentLoaded', function() {
            console.log('PulseChain News Dashboard loaded');
            loadDashboardData();
            
            // Refresh every 5 minutes
            setInterval(loadDashboardData, 5 * 60 * 1000);
        });
    </script>

    <!-- Include API Client -->
    <script src="/js/api.js"></script>
</body>
</html>