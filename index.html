<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>PulseChain News Hub</title>
    <link rel="stylesheet" href="css/main.css" />
    <link rel="stylesheet" href="css/tailwind.css" />
    <style>
        :root {
            --primary: #3B82F6;
            --primary-hover: #2563EB;
            --primary-light: #60A5FA;
            --primary-dark: #1E40AF;
            --secondary: #64748B;
            --accent: #F59E0B;
            --success: #10B981;
            --warning: #F59E0B;
            --error: #EF4444;
            --background: #FAFBFC;
            --surface: #FFFFFF;
            --text-primary: #1F2937;
            --text-secondary: #6B7280;
            --border: #E5E7EB;
            --border-light: #F3F4F6;
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            --radius-sm: 0.375rem;
            --radius-md: 0.5rem;
            --radius-lg: 0.75rem;
            --radius-xl: 1rem;
            --transition-fast: 150ms ease-in-out;
            --transition-normal: 250ms ease-in-out;
            --transition-slow: 350ms ease-in-out;
        }
        
        body {
            margin: 0;
            padding: 0;
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            color: white;
        }
        
        .loading-container {
            text-align: center;
            animation: fadeIn 0.8s ease-in;
        }
        
        .logo {
            font-size: 3rem;
            font-weight: bold;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .subtitle {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }
        
        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 4px solid rgba(255,255,255,0.3);
            border-top: 4px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }
        
        .loading-text {
            font-size: 1rem;
            opacity: 0.8;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
<script type="module" src="https://static.rocket.new/rocket-web.js?_cfg=https%3A%2F%2Fpulsechain6485back.builtwithrocket.new&_be=https%3A%2F%2Fapplication.rocket.new&_v=0.1.8"></script>
</head>
<body>
    <div class="loading-container">
        <div class="logo">📰 PulseChain News Hub</div>
        <div class="subtitle">Your Gateway to Real-Time News</div>
        <div class="loading-spinner"></div>
        <div class="loading-text">Loading your news dashboard...</div>
    </div>

    <script>
        // Redirect to news dashboard after a brief loading animation
        setTimeout(function() {
            window.location.href = 'pages/news_dashboard.html';
        }, 2000);
    </script>
<script id="dhws-dataInjector" src="/public/dhws-data-injector.js"></script>
</body>
</html>