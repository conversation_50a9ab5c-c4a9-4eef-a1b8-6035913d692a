<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Search Results - PulseChain News Hub</title>
    <link rel="stylesheet" href="../css/main.css" />
    <link rel="stylesheet" href="../css/tailwind.css" />
<script type="module" src="https://static.rocket.new/rocket-web.js?_cfg=https%3A%2F%2Fpulsechain6485back.builtwithrocket.new&_be=https%3A%2F%2Fapplication.rocket.new&_v=0.1.8"></script>
</head>
<body class="bg-background min-h-screen">
    <!-- Header Navigation -->
    <header class="bg-surface border-b border-border sticky top-0 z-50 shadow-card">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center justify-between h-16">
                <!-- Logo -->
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="h-8 w-8 text-primary" viewBox="0 0 32 32" fill="currentColor">
                            <path d="M16 2L4 8v16l12 6 12-6V8L16 2zm0 4l8 4v12l-8 4-8-4V10l8-4z"/>
                            <circle cx="16" cy="16" r="4" fill="currentColor"/>
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h1 class="text-xl font-bold text-text-primary">PulseChain News Hub</h1>
                    </div>
                </div>

                <!-- Navigation -->
                <nav class="hidden md:flex space-x-8">
                    <a href="/dashboard" class="text-text-secondary hover:text-primary px-3 py-2 rounded-md hover:bg-secondary-50 transition-colors">Dashboard</a>
                    <a href="/analysis" class="text-text-secondary hover:text-primary px-3 py-2 rounded-md hover:bg-secondary-50 transition-colors">Analysis</a>
                    <a href="/sources" class="text-text-secondary hover:text-primary px-3 py-2 rounded-md hover:bg-secondary-50 transition-colors">Sources</a>
                    <a href="/search" class="text-primary font-medium px-3 py-2 rounded-md bg-primary-50">Search</a>
                </nav>

                <!-- Mobile menu button -->
                <div class="md:hidden">
                    <button type="button" class="text-text-secondary hover:text-primary p-2" onclick="toggleMobileMenu()">
                        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/>
                        </svg>
                    </button>
                </div>
            </div>
        </div>

            <!-- Mobile Navigation -->
            <div id="mobile-menu" class="md:hidden hidden bg-surface border-t border-border">
                <div class="px-2 pt-2 pb-3 space-y-1">
                    <a href="/dashboard" class="block px-3 py-2 text-text-secondary hover:text-primary hover:bg-secondary-50 rounded-md">Dashboard</a>
                    <a href="/analysis" class="block px-3 py-2 text-text-secondary hover:text-primary hover:bg-secondary-50 rounded-md">Analysis</a>
                    <a href="/sources" class="block px-3 py-2 text-text-secondary hover:text-primary hover:bg-secondary-50 rounded-md">Sources</a>
                    <a href="/search" class="block px-3 py-2 text-primary font-medium bg-primary-50 rounded-md">Search</a>
                </div>
            </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <!-- Search Header -->
        <div class="mb-6">
            <div class="bg-surface rounded-lg shadow-card p-6 border border-border">
                <!-- Search Bar -->
                <div class="relative mb-4">
                    <input type="text" value="PulseChain DeFi adoption" placeholder="Search PulseChain news..." class="w-full pl-12 pr-4 py-3 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent text-base" />
                    <svg class="absolute left-4 top-3.5 h-5 w-5 text-text-secondary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                    </svg>
                    <button class="absolute right-3 top-2.5 btn-primary px-4 py-1.5 text-sm">
                        Search
                    </button>
                </div>

                <!-- Search Context -->
                <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                    <div class="flex items-center gap-4 text-sm text-text-secondary">
                        <span class="font-medium text-text-primary">Search Results for:</span>
                        <span class="bg-primary-50 text-primary px-3 py-1 rounded-full font-medium">"PulseChain DeFi adoption"</span>
                        <span>•</span>
                        <span class="font-data">1,247 results</span>
                        <span>•</span>
                        <span class="font-data">0.23s</span>
                    </div>
                    <button class="text-sm text-primary hover:text-primary-700 font-medium flex items-center gap-1">
                        <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707"/>
                        </svg>
                        Advanced Search
                    </button>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
            <!-- Filter Sidebar -->
            <div class="lg:col-span-1">
                <div class="bg-surface rounded-lg shadow-card p-4 border border-border sticky top-24">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="font-semibold text-text-primary">Filters</h3>
                        <button class="text-sm text-primary hover:text-primary-700 font-medium" onclick="clearAllFilters()">
                            Clear All
                        </button>
                    </div>

                    <!-- Active Filters -->
                    <div class="mb-4">
                        <div class="flex flex-wrap gap-2">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
                                Last 7 days
                                <button class="ml-1.5 h-3 w-3 text-primary-600 hover:text-primary-800">
                                    <svg fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"/>
                                    </svg>
                                </button>
                            </span>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-success-100 text-success-800">
                                Verified Sources
                                <button class="ml-1.5 h-3 w-3 text-success-600 hover:text-success-800">
                                    <svg fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"/>
                                    </svg>
                                </button>
                            </span>
                        </div>
                    </div>

                    <!-- Date Range Filter -->
                    <div class="mb-6">
                        <h4 class="font-medium text-text-primary mb-3">Date Range</h4>
                        <div class="space-y-2">
                            <label class="flex items-center">
                                <input type="radio" name="dateRange" value="24h" class="text-primary focus:ring-primary" />
                                <span class="ml-2 text-sm text-text-secondary">Last 24 hours</span>
                            </label>
                            <label class="flex items-center">
                                <input type="radio" name="dateRange" value="7d" checked class="text-primary focus:ring-primary" />
                                <span class="ml-2 text-sm text-text-secondary">Last 7 days</span>
                            </label>
                            <label class="flex items-center">
                                <input type="radio" name="dateRange" value="30d" class="text-primary focus:ring-primary" />
                                <span class="ml-2 text-sm text-text-secondary">Last 30 days</span>
                            </label>
                            <label class="flex items-center">
                                <input type="radio" name="dateRange" value="all" class="text-primary focus:ring-primary" />
                                <span class="ml-2 text-sm text-text-secondary">All time</span>
                            </label>
                        </div>
                    </div>

                    <!-- Source Credibility Filter -->
                    <div class="mb-6">
                        <h4 class="font-medium text-text-primary mb-3">Source Credibility</h4>
                        <div class="space-y-2">
                            <label class="flex items-center">
                                <input type="checkbox" checked class="text-primary focus:ring-primary rounded" />
                                <span class="ml-2 text-sm text-text-secondary">Verified Sources</span>
                                <span class="ml-auto text-xs text-success-600 font-medium">892</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="text-primary focus:ring-primary rounded" />
                                <span class="ml-2 text-sm text-text-secondary">Community Sources</span>
                                <span class="ml-auto text-xs text-warning-600 font-medium">234</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="text-primary focus:ring-primary rounded" />
                                <span class="ml-2 text-sm text-text-secondary">New Sources</span>
                                <span class="ml-auto text-xs text-text-secondary font-medium">121</span>
                            </label>
                        </div>
                    </div>

                    <!-- Sentiment Filter -->
                    <div class="mb-6">
                        <h4 class="font-medium text-text-primary mb-3">Sentiment</h4>
                        <div class="space-y-2">
                            <label class="flex items-center">
                                <input type="checkbox" class="text-primary focus:ring-primary rounded" />
                                <span class="ml-2 text-sm text-text-secondary">Positive</span>
                                <span class="ml-auto text-xs text-success-600 font-medium">687</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="text-primary focus:ring-primary rounded" />
                                <span class="ml-2 text-sm text-text-secondary">Neutral</span>
                                <span class="ml-auto text-xs text-warning-600 font-medium">423</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="text-primary focus:ring-primary rounded" />
                                <span class="ml-2 text-sm text-text-secondary">Negative</span>
                                <span class="ml-auto text-xs text-error-600 font-medium">137</span>
                            </label>
                        </div>
                    </div>

                    <!-- Content Type Filter -->
                    <div class="mb-6">
                        <h4 class="font-medium text-text-primary mb-3">Content Type</h4>
                        <div class="space-y-2">
                            <label class="flex items-center">
                                <input type="checkbox" checked class="text-primary focus:ring-primary rounded" />
                                <span class="ml-2 text-sm text-text-secondary">News Articles</span>
                                <span class="ml-auto text-xs text-text-secondary font-medium">1,089</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="text-primary focus:ring-primary rounded" />
                                <span class="ml-2 text-sm text-text-secondary">Analysis</span>
                                <span class="ml-auto text-xs text-text-secondary font-medium">98</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="text-primary focus:ring-primary rounded" />
                                <span class="ml-2 text-sm text-text-secondary">Press Releases</span>
                                <span class="ml-auto text-xs text-text-secondary font-medium">60</span>
                            </label>
                        </div>
                    </div>

                    <!-- Apply Filters Button -->
                    <button class="w-full btn-primary py-2">
                        Apply Filters
                    </button>
                </div>
            </div>

            <!-- Search Results -->
            <div class="lg:col-span-3">
                <!-- Sort Controls -->
                <div class="bg-surface rounded-lg shadow-card p-4 mb-6 border border-border">
                    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                        <div class="flex items-center gap-4">
                            <span class="text-sm font-medium text-text-primary">Sort by:</span>
                            <div class="flex gap-2">
                                <button class="px-3 py-1.5 text-sm bg-primary text-white rounded-md font-medium">
                                    Relevance
                                </button>
                                <button class="px-3 py-1.5 text-sm text-text-secondary hover:text-primary hover:bg-secondary-50 rounded-md">
                                    Date
                                </button>
                                <button class="px-3 py-1.5 text-sm text-text-secondary hover:text-primary hover:bg-secondary-50 rounded-md">
                                    Credibility
                                </button>
                                <button class="px-3 py-1.5 text-sm text-text-secondary hover:text-primary hover:bg-secondary-50 rounded-md">
                                    Engagement
                                </button>
                            </div>
                        </div>
                        <div class="flex items-center gap-2">
                            <span class="text-sm text-text-secondary">View:</span>
                            <button class="p-1.5 text-primary bg-primary-50 rounded">
                                <svg class="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z"/>
                                </svg>
                            </button>
                            <button class="p-1.5 text-text-secondary hover:text-primary hover:bg-secondary-50 rounded">
                                <svg class="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Search Suggestions -->
                <div class="bg-accent-50 border border-accent-200 rounded-lg p-4 mb-6">
                    <div class="flex items-start gap-3">
                        <svg class="h-5 w-5 text-accent-600 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
                        </svg>
                        <div>
                            <h4 class="font-medium text-accent-800 mb-2">Related Searches</h4>
                            <div class="flex flex-wrap gap-2">
                                <button class="px-3 py-1 bg-accent-100 text-accent-800 rounded-full text-sm hover:bg-accent-200 transition-colors">
                                    PulseChain TVL growth
                                </button>
                                <button class="px-3 py-1 bg-accent-100 text-accent-800 rounded-full text-sm hover:bg-accent-200 transition-colors">
                                    DeFi protocols PulseChain
                                </button>
                                <button class="px-3 py-1 bg-accent-100 text-accent-800 rounded-full text-sm hover:bg-accent-200 transition-colors">
                                    PLS token staking
                                </button>
                                <button class="px-3 py-1 bg-accent-100 text-accent-800 rounded-full text-sm hover:bg-accent-200 transition-colors">
                                    Cross-chain bridges
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Results List -->
                <div class="space-y-4">
                    <!-- Result Item 1 -->
                    <article class="card p-6 hover:shadow-hover transition-all duration-200 border-accent-left">
                        <div class="flex flex-col lg:flex-row gap-4">
                            <div class="lg:w-48 flex-shrink-0">
                                <img src="https://images.unsplash.com/photo-1639762681485-074b7f938ba0?q=80&w=2832&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" alt="PulseChain DeFi ecosystem" class="w-full h-32 lg:h-24 object-cover rounded-md" loading="lazy" onerror="this.src='https://images.unsplash.com/photo-1584824486509-112e4181ff6b?q=80&w=2940&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D'; this.onerror=null;" />
                            </div>

                            <div class="flex-1">
                                <div class="flex items-start justify-between mb-2">
                                    <div class="flex items-center gap-2 text-sm text-text-secondary">
                                        <span class="credibility-badge credibility-verified">
                                            <svg class="h-3 w-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                            </svg>
                                            Verified
                                        </span>
                                        <span>CryptoNews Daily</span>
                                        <span>•</span>
                                        <span>2 hours ago</span>
                                        <span>•</span>
                                        <span class="text-success-600 font-medium">Positive</span>
                                        <span>•</span>
                                        <span class="font-data text-primary font-medium">98% relevance</span>
                                    </div>
                                    <div class="flex items-center gap-2">
                                        <button class="text-text-secondary hover:text-primary p-1" title="Quick Preview" onclick="showPreview(this)">
                                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                                            </svg>
                                        </button>
                                        <button class="text-text-secondary hover:text-primary p-1" title="Bookmark">
                                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"/>
                                            </svg>
                                        </button>
                                        <button class="text-text-secondary hover:text-primary p-1" title="Share">
                                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"/>
                                            </svg>
                                        </button>
                                    </div>
                                </div>

                                <h2 class="text-lg font-semibold text-text-primary mb-2 hover:text-primary cursor-pointer">
                                    <a href="article_analysis.html">PulseChain DeFi Ecosystem Reaches $500M TVL Milestone as Adoption Accelerates</a>
                                </h2>

                                <p class="text-text-secondary text-sm mb-3 line-clamp-2">
                                    The PulseChain decentralized finance ecosystem has achieved a significant milestone with total value locked (TVL) surpassing $500 million, driven by increased adoption of native DeFi protocols and cross-chain bridge integrations. This growth represents a 340% increase from the previous quarter.
                                </p>

                                <div class="flex items-center justify-between">
                                    <div class="flex items-center gap-4 text-xs text-text-secondary">
                                        <span class="flex items-center gap-1">
                                            <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                                            </svg>
                                            2.1k views
                                        </span>
                                        <span class="flex items-center gap-1">
                                            <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"/>
                                            </svg>
                                            89 comments
                                        </span>
                                        <span class="flex items-center gap-1">
                                            <svg class="h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
                                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                            </svg>
                                            4.9 rating
                                        </span>
                                    </div>
                                    <button class="btn-primary text-xs px-3 py-1">
                                        Read Full Article
                                    </button>
                                </div>

                                <!-- Quick Preview (Hidden by default) -->
                                <div class="preview-content hidden mt-4 pt-4 border-t border-border">
                                    <div class="bg-secondary-50 rounded-lg p-4">
                                        <h4 class="font-medium text-text-primary mb-2 flex items-center gap-2">
                                            <svg class="h-4 w-4 text-primary" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"/>
                                            </svg>
                                            AI Summary
                                        </h4>
                                        <p class="text-sm text-text-primary mb-3">
                                            PulseChain's DeFi ecosystem growth is driven by lower transaction fees, faster settlement times, and successful integration with major DeFi protocols. Key metrics show 85% of TVL comes from native protocols, with lending and DEX platforms leading adoption.
                                        </p>
                                        <div class="flex gap-2">
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-success-100 text-success-800">
                                                Strong Growth
                                            </span>
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
                                                High Relevance
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </article>

                    <!-- Result Item 2 -->
                    <article class="card p-6 hover:shadow-hover transition-all duration-200">
                        <div class="flex flex-col lg:flex-row gap-4">
                            <div class="lg:w-48 flex-shrink-0">
                                <img src="https://images.pexels.com/photos/730547/pexels-photo-730547.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2" alt="DeFi trading interface" class="w-full h-32 lg:h-24 object-cover rounded-md" loading="lazy" onerror="this.src='https://images.unsplash.com/photo-1584824486509-112e4181ff6b?q=80&w=2940&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D'; this.onerror=null;" />
                            </div>

                            <div class="flex-1">
                                <div class="flex items-start justify-between mb-2">
                                    <div class="flex items-center gap-2 text-sm text-text-secondary">
                                        <span class="credibility-badge credibility-verified">
                                            <svg class="h-3 w-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                            </svg>
                                            Verified
                                        </span>
                                        <span>DeFi Analytics Pro</span>
                                        <span>•</span>
                                        <span>4 hours ago</span>
                                        <span>•</span>
                                        <span class="text-success-600 font-medium">Positive</span>
                                        <span>•</span>
                                        <span class="font-data text-primary font-medium">94% relevance</span>
                                    </div>
                                    <div class="flex items-center gap-2">
                                        <button class="text-text-secondary hover:text-primary p-1" title="Quick Preview" onclick="showPreview(this)">
                                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                                            </svg>
                                        </button>
                                        <button class="text-text-secondary hover:text-primary p-1" title="Bookmark">
                                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"/>
                                            </svg>
                                        </button>
                                        <button class="text-text-secondary hover:text-primary p-1" title="Share">
                                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"/>
                                            </svg>
                                        </button>
                                    </div>
                                </div>

                                <h2 class="text-lg font-semibold text-text-primary mb-2 hover:text-primary cursor-pointer">
                                    <a href="article_analysis.html">Top 5 DeFi Protocols Driving PulseChain Adoption: Complete Analysis</a>
                                </h2>

                                <p class="text-text-secondary text-sm mb-3 line-clamp-2">
                                    Comprehensive analysis of the leading decentralized finance protocols on PulseChain, examining their contribution to ecosystem growth, user adoption metrics, and future development roadmaps.
                                </p>

                                <div class="flex items-center justify-between">
                                    <div class="flex items-center gap-4 text-xs text-text-secondary">
                                        <span class="flex items-center gap-1">
                                            <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                                            </svg>
                                            1.8k views
                                        </span>
                                        <span class="flex items-center gap-1">
                                            <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"/>
                                            </svg>
                                            67 comments
                                        </span>
                                        <span class="flex items-center gap-1">
                                            <svg class="h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
                                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                            </svg>
                                            4.7 rating
                                        </span>
                                    </div>
                                    <button class="btn-primary text-xs px-3 py-1">
                                        Read Full Article
                                    </button>
                                </div>
                            </div>
                        </div>
                    </article>

                    <!-- Result Item 3 -->
                    <article class="card p-6 hover:shadow-hover transition-all duration-200">
                        <div class="flex flex-col lg:flex-row gap-4">
                            <div class="lg:w-48 flex-shrink-0">
                                <img src="https://images.pixabay.com/photo/2018/01/18/07/31/bitcoin-3089728_1280.jpg" alt="Cryptocurrency market analysis" class="w-full h-32 lg:h-24 object-cover rounded-md" loading="lazy" onerror="this.src='https://images.unsplash.com/photo-1584824486509-112e4181ff6b?q=80&w=2940&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D'; this.onerror=null;" />
                            </div>

                            <div class="flex-1">
                                <div class="flex items-start justify-between mb-2">
                                    <div class="flex items-center gap-2 text-sm text-text-secondary">
                                        <span class="credibility-badge credibility-warning">
                                            <svg class="h-3 w-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                                            </svg>
                                            Community
                                        </span>
                                        <span>BlockchainBuzz</span>
                                        <span>•</span>
                                        <span>6 hours ago</span>
                                        <span>•</span>
                                        <span class="text-warning-600 font-medium">Neutral</span>
                                        <span>•</span>
                                        <span class="font-data text-primary font-medium">87% relevance</span>
                                    </div>
                                    <div class="flex items-center gap-2">
                                        <button class="text-text-secondary hover:text-primary p-1" title="Quick Preview" onclick="showPreview(this)">
                                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                                            </svg>
                                        </button>
                                        <button class="text-text-secondary hover:text-primary p-1" title="Bookmark">
                                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"/>
                                            </svg>
                                        </button>
                                        <button class="text-text-secondary hover:text-primary p-1" title="Share">
                                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"/>
                                            </svg>
                                        </button>
                                    </div>
                                </div>

                                <h2 class="text-lg font-semibold text-text-primary mb-2 hover:text-primary cursor-pointer">
                                    <a href="article_analysis.html">PulseChain DeFi Yield Farming: Opportunities and Risk Assessment</a>
                                </h2>

                                <p class="text-text-secondary text-sm mb-3 line-clamp-2">
                                    Exploring yield farming opportunities within the PulseChain DeFi ecosystem, including risk analysis, APY comparisons, and strategic considerations for liquidity providers seeking optimal returns.
                                </p>

                                <div class="flex items-center justify-between">
                                    <div class="flex items-center gap-4 text-xs text-text-secondary">
                                        <span class="flex items-center gap-1">
                                            <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                                            </svg>
                                            1.3k views
                                        </span>
                                        <span class="flex items-center gap-1">
                                            <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"/>
                                            </svg>
                                            34 comments
                                        </span>
                                        <span class="flex items-center gap-1">
                                            <svg class="h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
                                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                            </svg>
                                            4.2 rating
                                        </span>
                                    </div>
                                    <button class="btn-primary text-xs px-3 py-1">
                                        Read Full Article
                                    </button>
                                </div>
                            </div>
                        </div>
                    </article>
                </div>

                <!-- Pagination -->
                <div class="flex items-center justify-between mt-8 pt-6 border-t border-border">
                    <div class="flex items-center gap-2 text-sm text-text-secondary">
                        <span>Showing</span>
                        <span class="font-medium text-text-primary">1-20</span>
                        <span>of</span>
                        <span class="font-medium text-text-primary">1,247</span>
                        <span>results</span>
                    </div>
                    <div class="flex items-center gap-2">
                        <button class="px-3 py-2 text-sm text-text-secondary hover:text-primary hover:bg-secondary-50 rounded-md border border-border disabled:opacity-50" disabled>
                            Previous
                        </button>
                        <button class="px-3 py-2 text-sm bg-primary text-white rounded-md">1</button>
                        <button class="px-3 py-2 text-sm text-text-secondary hover:text-primary hover:bg-secondary-50 rounded-md">2</button>
                        <button class="px-3 py-2 text-sm text-text-secondary hover:text-primary hover:bg-secondary-50 rounded-md">3</button>
                        <span class="px-2 text-text-secondary">...</span>
                        <button class="px-3 py-2 text-sm text-text-secondary hover:text-primary hover:bg-secondary-50 rounded-md">63</button>
                        <button class="px-3 py-2 text-sm text-text-secondary hover:text-primary hover:bg-secondary-50 rounded-md border border-border">
                            Next
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Search History Sidebar (Hidden by default) -->
    <div id="search-history" class="fixed inset-y-0 right-0 w-80 bg-surface shadow-floating border-l border-border transform translate-x-full transition-transform duration-300 z-50 hidden">
        <div class="p-4 border-b border-border">
            <div class="flex items-center justify-between">
                <h3 class="font-semibold text-text-primary">Search History</h3>
                <button onclick="toggleSearchHistory()" class="text-text-secondary hover:text-primary">
                    <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                    </svg>
                </button>
            </div>
        </div>
        <div class="p-4 space-y-3">
            <div class="flex items-center justify-between p-2 hover:bg-secondary-50 rounded-md cursor-pointer">
                <span class="text-sm text-text-primary">PulseChain DeFi adoption</span>
                <span class="text-xs text-text-secondary">2h ago</span>
            </div>
            <div class="flex items-center justify-between p-2 hover:bg-secondary-50 rounded-md cursor-pointer">
                <span class="text-sm text-text-primary">PLS token staking</span>
                <span class="text-xs text-text-secondary">1d ago</span>
            </div>
            <div class="flex items-center justify-between p-2 hover:bg-secondary-50 rounded-md cursor-pointer">
                <span class="text-sm text-text-primary">Cross-chain bridges</span>
                <span class="text-xs text-text-secondary">3d ago</span>
            </div>
        </div>
    </div>

    <script>
        // Mobile menu toggle
        function toggleMobileMenu() {
            const menu = document.getElementById('mobile-menu');
            menu.classList.toggle('hidden');
        }

        // Clear all filters
        function clearAllFilters() {
            // Reset all checkboxes and radio buttons
            document.querySelectorAll('input[type="checkbox"]').forEach(cb => cb.checked = false);
            document.querySelectorAll('input[type="radio"]').forEach(rb => rb.checked = false);
            
            // Clear active filter badges
            document.querySelectorAll('.inline-flex.items-center.px-2\\.5').forEach(badge => {
                if (badge.textContent.includes('Last 7 days') || badge.textContent.includes('Verified Sources')) {
                    badge.remove();
                }
            });
            
            console.log('All filters cleared');
        }

        // Show quick preview
        function showPreview(button) {
            const article = button.closest('article');
            const previewContent = article.querySelector('.preview-content');
            
            if (previewContent) {
                if (previewContent.classList.contains('hidden')) {
                    previewContent.classList.remove('hidden');
                    button.setAttribute('title', 'Hide Preview');
                } else {
                    previewContent.classList.add('hidden');
                    button.setAttribute('title', 'Quick Preview');
                }
            }
        }

        // Toggle search history sidebar
        function toggleSearchHistory() {
            const sidebar = document.getElementById('search-history');
            sidebar.classList.toggle('hidden');
            if (!sidebar.classList.contains('hidden')) {
                setTimeout(() => {
                    sidebar.classList.toggle('translate-x-full');
                }, 10);
            } else {
                sidebar.classList.add('translate-x-full');
            }
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Search Results page loaded');
            
            // Add search functionality
            const searchInput = document.querySelector('input[type="text"]');
            searchInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    performSearch();
                }
            });
        });

        // Perform search function
        function performSearch() {
            const query = document.querySelector('input[type="text"]').value;
            console.log('Searching for:', query);
            // In a real application, this would trigger a new search
        }

        // Simulate loading more results
        function loadMoreResults() {
            console.log('Loading more search results...');
            // In a real application, this would load additional results
        }
    </script>
<script id="dhws-dataInjector" src="../public/dhws-data-injector.js"></script>
</body>
</html>