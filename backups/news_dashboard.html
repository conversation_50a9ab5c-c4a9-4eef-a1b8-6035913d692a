<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PulseChain News Hub - Dashboard</title>
    <link rel="stylesheet" href="/css/main.css">
    <link rel="stylesheet" href="/css/tailwind.css">
    <style>
        .loading-spinner {
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        .article-card {
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        .article-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <h1 class="text-2xl font-bold text-gray-900">📰 PulseChain News Hub</h1>
                </div>
                <nav class="flex space-x-4">
                    <a href="/dashboard" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">Dashboard</a>
                    <a href="/analysis" class="text-gray-600 hover:text-blue-600 px-4 py-2 rounded-md">Analysis</a>
                    <a href="/sources" class="text-gray-600 hover:text-blue-600 px-4 py-2 rounded-md">Sources</a>
                    <button id="refresh-btn" class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700">
                        Refresh Data
                    </button>
                </nav>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- News Articles Section -->
            <div class="lg:col-span-2">
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex justify-between items-center mb-6">
                        <h2 class="text-xl font-semibold text-gray-900">Latest PulseChain News</h2>
                        <div class="text-sm text-gray-500" id="articles-count">Loading...</div>
                    </div>

                    <!-- Articles Container -->
                    <div id="articles-container" class="space-y-4">
                        <div class="text-center py-8">
                            <div class="loading-spinner inline-block w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full"></div>
                            <p class="mt-2 text-gray-600">Loading articles...</p>
                        </div>
                    </div>

                    <!-- Load More Button -->
                    <div class="text-center mt-6">
                        <button id="load-more-btn" class="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 hidden">
                            Load More Articles
                        </button>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="lg:col-span-1 space-y-6">
                <!-- Stats Card -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Statistics</h3>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-gray-600">Total Articles:</span>
                            <span class="font-semibold" id="total-articles">Loading...</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Today's Articles:</span>
                            <span class="font-semibold" id="today-articles">Loading...</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Active Sources:</span>
                            <span class="font-semibold" id="active-sources">Loading...</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Last Updated:</span>
                            <span class="font-semibold text-xs" id="last-updated">Loading...</span>
                        </div>
                    </div>
                </div>

                <!-- Price Data Card -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">PulseChain Prices</h3>
                    <div id="price-container" class="space-y-3">
                        <div class="text-center py-4">
                            <div class="loading-spinner inline-block w-6 h-6 border-2 border-blue-600 border-t-transparent rounded-full"></div>
                            <p class="mt-2 text-sm text-gray-600">Loading prices...</p>
                        </div>
                    </div>
                </div>

                <!-- Trading Signals -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Trading Signals</h3>
                    <div id="signals-container" class="space-y-3">
                        <div class="text-center py-4">
                            <p class="text-sm text-gray-600">Loading signals...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Include API Client -->
    <script src="/js/api.js"></script>
    
    <script>
        // Global state
        let currentOffset = 0;
        let isLoading = false;

        // DOM elements
        const articlesContainer = document.getElementById('articles-container');
        const articlesCount = document.getElementById('articles-count');
        const loadMoreBtn = document.getElementById('load-more-btn');
        const refreshBtn = document.getElementById('refresh-btn');

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Dashboard initializing...');
            loadDashboard();
            setupEventListeners();
        });

        // Setup event listeners
        function setupEventListeners() {
            refreshBtn.addEventListener('click', () => {
                currentOffset = 0;
                loadDashboard();
            });

            loadMoreBtn.addEventListener('click', loadMoreArticles);
        }

        // Main dashboard loader
        async function loadDashboard() {
            if (isLoading) return;
            isLoading = true;

            try {
                console.log('Loading dashboard data...');
                
                // Load all data in parallel
                const [articlesResponse, statsResponse, pricesResponse, signalsResponse] = await Promise.all([
                    PulseChainAPI.getArticles({ limit: 10, offset: 0 }),
                    PulseChainAPI.getDashboardData(),
                    PulseChainAPI.getDexData(),
                    PulseChainAPI.getTradingSignals()
                ]);

                console.log('API responses:', { articlesResponse, statsResponse, pricesResponse, signalsResponse });

                // Update articles
                if (articlesResponse.success) {
                    displayArticles(articlesResponse.data, true);
                    articlesCount.textContent = `${articlesResponse.data.length} articles`;
                    loadMoreBtn.classList.remove('hidden');
                }

                // Update stats
                if (statsResponse.success) {
                    updateStats(statsResponse.data.stats);
                }

                // Update prices
                if (pricesResponse.success) {
                    updatePrices(pricesResponse.data);
                }

                // Update signals
                if (signalsResponse.success) {
                    updateSignals(signalsResponse.data);
                }

            } catch (error) {
                console.error('Dashboard loading error:', error);
                showError('Failed to load dashboard data: ' + error.message);
            } finally {
                isLoading = false;
            }
        }

        // Display articles
        function displayArticles(articles, clearFirst = false) {
            if (clearFirst) {
                articlesContainer.innerHTML = '';
                currentOffset = 0;
            }

            if (!articles || articles.length === 0) {
                articlesContainer.innerHTML = `
                    <div class="text-center py-8">
                        <p class="text-gray-600">No articles available</p>
                        <button onclick="loadDashboard()" class="mt-2 bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                            Try Again
                        </button>
                    </div>
                `;
                return;
            }

            articles.forEach(article => {
                const articleElement = createArticleElement(article);
                articlesContainer.appendChild(articleElement);
            });

            currentOffset += articles.length;
        }

        // Create article element
        function createArticleElement(article) {
            const div = document.createElement('div');
            div.className = 'article-card border rounded-lg p-4 hover:shadow-md cursor-pointer';
            
            // Format date
            const publishedDate = new Date(article.published_at).toLocaleDateString();
            
            // Get sentiment color
            let sentimentColor = 'text-gray-600';
            if (article.sentiment_label === 'positive') sentimentColor = 'text-green-600';
            if (article.sentiment_label === 'negative') sentimentColor = 'text-red-600';

            div.innerHTML = `
                <div class="flex justify-between items-start mb-2">
                    <h3 class="text-lg font-semibold text-gray-900 line-clamp-2">${article.title}</h3>
                    <span class="text-xs text-gray-500 ml-2">${publishedDate}</span>
                </div>
                
                <p class="text-gray-600 text-sm mb-3 line-clamp-2">${article.description || 'No description available'}</p>
                
                <div class="flex justify-between items-center">
                    <div class="flex space-x-4 text-xs">
                        <span class="text-gray-500">Source: ${article.source_name}</span>
                        <span class="${sentimentColor}">
                            Sentiment: ${article.sentiment_label || 'neutral'}
                        </span>
                        <span class="text-gray-500">
                            Credibility: ${article.source_credibility ? article.source_credibility.toFixed(1) : 'N/A'}/10
                        </span>
                    </div>
                    <button onclick="viewArticle(${article.id})" class="bg-blue-600 text-white text-xs px-3 py-1 rounded hover:bg-blue-700">
                        Read More
                    </button>
                </div>
            `;

            return div;
        }

        // Update stats
        function updateStats(stats) {
            if (!stats) return;
            
            document.getElementById('total-articles').textContent = stats.totalArticles || '0';
            document.getElementById('today-articles').textContent = stats.articlesLast24h || '0';
            document.getElementById('active-sources').textContent = stats.activeSources || '0';
            
            if (stats.lastUpdated) {
                const lastUpdated = new Date(stats.lastUpdated).toLocaleString();
                document.getElementById('last-updated').textContent = lastUpdated;
            }
        }

        // Update prices
        function updatePrices(dexData) {
            const container = document.getElementById('price-container');
            
            if (!dexData || !dexData.prices) {
                container.innerHTML = '<p class="text-gray-600 text-sm">No price data available</p>';
                return;
            }

            const prices = dexData.prices;
            const priceElements = Object.entries(prices).map(([symbol, data]) => {
                const changeColor = data.change24h >= 0 ? 'text-green-600' : 'text-red-600';
                const changeSign = data.change24h >= 0 ? '+' : '';
                
                return `
                    <div class="flex justify-between items-center">
                        <span class="font-semibold">${symbol}</span>
                        <div class="text-right">
                            <div class="font-semibold">$${parseFloat(data.price).toFixed(6)}</div>
                            <div class="text-xs ${changeColor}">${changeSign}${data.change24h.toFixed(2)}%</div>
                        </div>
                    </div>
                `;
            }).join('');

            container.innerHTML = priceElements;
        }

        // Update signals
        function updateSignals(signals) {
            const container = document.getElementById('signals-container');
            
            if (!signals || signals.length === 0) {
                container.innerHTML = '<p class="text-gray-600 text-sm">No active trading signals</p>';
                return;
            }

            const signalElements = signals.slice(0, 3).map(signal => {
                const signalColor = signal.signal === 'BUY' ? 'text-green-600' : 
                                  signal.signal === 'SELL' ? 'text-red-600' : 'text-yellow-600';
                
                return `
                    <div class="border-l-4 border-blue-600 pl-3">
                        <div class="font-semibold ${signalColor}">${signal.signal}</div>
                        <div class="text-sm text-gray-600">${signal.token || 'PLS'}</div>
                        <div class="text-xs text-gray-500">Confidence: ${Math.round((signal.confidence || 0.5) * 100)}%</div>
                    </div>
                `;
            }).join('');

            container.innerHTML = signalElements;
        }

        // Load more articles
        async function loadMoreArticles() {
            if (isLoading) return;
            isLoading = true;

            try {
                const response = await PulseChainAPI.getArticles({ 
                    limit: 10, 
                    offset: currentOffset 
                });

                if (response.success && response.data.length > 0) {
                    displayArticles(response.data, false);
                } else {
                    loadMoreBtn.textContent = 'No more articles';
                    loadMoreBtn.disabled = true;
                }
            } catch (error) {
                console.error('Load more error:', error);
                showError('Failed to load more articles');
            } finally {
                isLoading = false;
            }
        }

        // View article details
        function viewArticle(articleId) {
            window.location.href = `/analysis?id=${articleId}`;
        }

        // Show error
        function showError(message) {
            const errorDiv = document.createElement('div');
            errorDiv.className = 'bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4';
            errorDiv.innerHTML = `
                <strong>Error:</strong> ${message}
                <button onclick="this.parentElement.remove()" class="float-right text-red-700 hover:text-red-900">×</button>
            `;
            articlesContainer.insertBefore(errorDiv, articlesContainer.firstChild);
        }

        // Auto-refresh every 5 minutes
        setInterval(() => {
            console.log('Auto-refreshing dashboard...');
            loadDashboard();
        }, 5 * 60 * 1000);
    </script>
</body>
</html>