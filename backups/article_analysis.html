<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Article Analysis - PulseChain News Hub</title>
    <link rel="stylesheet" href="/css/main.css">
    <link rel="stylesheet" href="/css/tailwind.css">
    <style>
        .loading-spinner {
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        .sentiment-positive { color: #10B981; }
        .sentiment-negative { color: #EF4444; }
        .sentiment-neutral { color: #6B7280; }
        .progress-bar {
            transition: width 0.3s ease-in-out;
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <h1 class="text-2xl font-bold text-gray-900">📰 PulseChain News Hub</h1>
                </div>
                <nav class="flex space-x-4">
                    <a href="/dashboard" class="text-gray-600 hover:text-blue-600 px-4 py-2 rounded-md">Dashboard</a>
                    <a href="/analysis" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">Analysis</a>
                    <a href="/sources" class="text-gray-600 hover:text-blue-600 px-4 py-2 rounded-md">Sources</a>
                    <button onclick="history.back()" class="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700">
                        ← Back
                    </button>
                </nav>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Loading State -->
        <div id="loading-state" class="text-center py-12">
            <div class="loading-spinner inline-block w-12 h-12 border-4 border-blue-600 border-t-transparent rounded-full"></div>
            <p class="mt-4 text-gray-600">Loading article analysis...</p>
        </div>

        <!-- Error State -->
        <div id="error-state" class="hidden bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            <h3 class="font-bold">Error</h3>
            <p id="error-message">Failed to load article</p>
            <button onclick="loadArticle()" class="mt-2 bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700">
                Try Again
            </button>
        </div>

        <!-- Article Content -->
        <div id="article-content" class="hidden space-y-6">
            <!-- Article Header -->
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex justify-between items-start mb-4">
                    <div class="flex-1">
                        <h1 id="article-title" class="text-3xl font-bold text-gray-900 mb-2">Article Title</h1>
                        <div class="flex flex-wrap gap-4 text-sm text-gray-600">
                            <span>Source: <span id="article-source" class="font-semibold">Loading...</span></span>
                            <span>Published: <span id="article-date" class="font-semibold">Loading...</span></span>
                            <span>Credibility: <span id="article-credibility" class="font-semibold">Loading...</span>/10</span>
                        </div>
                    </div>
                    <a id="article-url" href="#" target="_blank" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 ml-4">
                        View Original
                    </a>
                </div>
                
                <div id="article-description" class="text-gray-700 text-lg leading-relaxed">
                    Loading article description...
                </div>
            </div>

            <!-- AI Analysis -->
            <div class="bg-white rounded-lg shadow p-6">
                <h2 class="text-2xl font-bold text-gray-900 mb-4">🤖 AI Analysis</h2>
                
                <!-- Sentiment Analysis -->
                <div class="mb-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-3">Sentiment Analysis</h3>
                    <div class="flex items-center space-x-4">
                        <div class="flex-1">
                            <div class="bg-gray-200 rounded-full h-4">
                                <div id="sentiment-bar" class="progress-bar h-4 rounded-full bg-blue-600" style="width: 0%"></div>
                            </div>
                        </div>
                        <div class="text-right">
                            <div id="sentiment-label" class="font-bold text-lg">Loading...</div>
                            <div id="sentiment-score" class="text-sm text-gray-600">Score: 0</div>
                            <div id="sentiment-confidence" class="text-xs text-gray-500">Confidence: 0%</div>
                        </div>
                    </div>
                </div>

                <!-- Trading Signal -->
                <div id="trading-signal-section" class="mb-6 hidden">
                    <h3 class="text-lg font-semibold text-gray-900 mb-3">Trading Signal</h3>
                    <div class="bg-gray-50 rounded-lg p-4">
                        <div class="flex justify-between items-center">
                            <div>
                                <div id="trading-signal" class="text-2xl font-bold">HOLD</div>
                                <div id="signal-timeframe" class="text-sm text-gray-600">Timeframe: Medium</div>
                            </div>
                            <div class="text-right">
                                <div id="signal-confidence" class="text-lg font-semibold">0%</div>
                                <div class="text-xs text-gray-500">Confidence</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Key Insights -->
                <div id="key-insights-section" class="mb-6 hidden">
                    <h3 class="text-lg font-semibold text-gray-900 mb-3">Key Insights</h3>
                    <ul id="key-insights" class="space-y-2">
                        <!-- Insights will be populated here -->
                    </ul>
                </div>

                <!-- Price Impact -->
                <div id="price-impact-section" class="hidden">
                    <h3 class="text-lg font-semibold text-gray-900 mb-3">Predicted Price Impact</h3>
                    <div class="grid grid-cols-2 gap-4">
                        <div class="bg-gray-50 rounded-lg p-4">
                            <div class="text-sm text-gray-600">Short-term (1-7 days)</div>
                            <div id="price-impact-short" class="text-lg font-bold">0%</div>
                        </div>
                        <div class="bg-gray-50 rounded-lg p-4">
                            <div class="text-sm text-gray-600">Long-term (1-4 weeks)</div>
                            <div id="price-impact-long" class="text-lg font-bold">0%</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Related Articles -->
            <div class="bg-white rounded-lg shadow p-6">
                <h2 class="text-2xl font-bold text-gray-900 mb-4">Related Articles</h2>
                <div id="related-articles" class="space-y-4">
                    <p class="text-gray-600">Loading related articles...</p>
                </div>
            </div>
        </div>
    </main>

    <!-- Include API Client -->
    <script src="/js/api.js"></script>
    
    <script>
        // Get article ID from URL
        const urlParams = new URLSearchParams(window.location.search);
        const articleId = urlParams.get('id');

        // DOM elements
        const loadingState = document.getElementById('loading-state');
        const errorState = document.getElementById('error-state');
        const articleContent = document.getElementById('article-content');
        const errorMessage = document.getElementById('error-message');

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Article analysis page initializing...');
            if (articleId) {
                loadArticle();
            } else {
                showError('No article ID provided');
            }
        });

        // Load article data
        async function loadArticle() {
            if (!articleId) {
                showError('No article ID provided');
                return;
            }

            try {
                console.log('Loading article:', articleId);
                
                showLoading();
                const response = await PulseChainAPI.getArticle(articleId);

                if (response.success) {
                    displayArticle(response.data);
                    hideLoading();
                } else {
                    throw new Error('Article not found');
                }

            } catch (error) {
                console.error('Article loading error:', error);
                showError('Failed to load article: ' + error.message);
            }
        }

        // Display article
        function displayArticle(article) {
            // Basic article info
            document.getElementById('article-title').textContent = article.title;
            document.getElementById('article-source').textContent = article.source_name || 'Unknown';
            document.getElementById('article-date').textContent = new Date(article.published_at).toLocaleDateString();
            document.getElementById('article-credibility').textContent = (article.source_credibility || 0).toFixed(1);
            document.getElementById('article-description').textContent = article.description || 'No description available';
            
            if (article.url) {
                document.getElementById('article-url').href = article.url;
            }

            // Sentiment Analysis
            displaySentiment(article);

            // Trading Signal
            if (article.trading_signal) {
                displayTradingSignal(article);
            }

            // Key Insights
            if (article.key_insights) {
                displayKeyInsights(article.key_insights);
            }

            // Price Impact
            if (article.price_impact_short || article.price_impact_long) {
                displayPriceImpact(article);
            }

            // Load related articles (simplified)
            loadRelatedArticles(article.sentiment_label);
        }

        // Display sentiment analysis
        function displaySentiment(article) {
            const sentimentLabel = article.sentiment_label || 'neutral';
            const sentimentScore = article.sentiment_score || 0;
            const sentimentConfidence = article.sentiment_confidence || 0;

            document.getElementById('sentiment-label').textContent = sentimentLabel.toUpperCase();
            document.getElementById('sentiment-score').textContent = `Score: ${sentimentScore.toFixed(2)}`;
            document.getElementById('sentiment-confidence').textContent = `Confidence: ${Math.round(sentimentConfidence * 100)}%`;

            // Update progress bar and color
            const sentimentBar = document.getElementById('sentiment-bar');
            const normalizedScore = Math.abs(sentimentScore) * 100;
            sentimentBar.style.width = `${Math.min(normalizedScore, 100)}%`;

            // Set color based on sentiment
            const labelElement = document.getElementById('sentiment-label');
            if (sentimentLabel === 'positive') {
                labelElement.className = 'font-bold text-lg sentiment-positive';
                sentimentBar.className = 'progress-bar h-4 rounded-full bg-green-500';
            } else if (sentimentLabel === 'negative') {
                labelElement.className = 'font-bold text-lg sentiment-negative';
                sentimentBar.className = 'progress-bar h-4 rounded-full bg-red-500';
            } else {
                labelElement.className = 'font-bold text-lg sentiment-neutral';
                sentimentBar.className = 'progress-bar h-4 rounded-full bg-gray-500';
            }
        }

        // Display trading signal
        function displayTradingSignal(article) {
            const section = document.getElementById('trading-signal-section');
            section.classList.remove('hidden');

            document.getElementById('trading-signal').textContent = article.trading_signal || 'HOLD';
            document.getElementById('signal-timeframe').textContent = `Timeframe: ${article.signal_timeframe || 'Medium'}`;
            document.getElementById('signal-confidence').textContent = `${Math.round((article.signal_confidence || 0.5) * 100)}%`;

            // Set signal color
            const signalElement = document.getElementById('trading-signal');
            const signal = article.trading_signal;
            if (signal === 'BUY') {
                signalElement.className = 'text-2xl font-bold text-green-600';
            } else if (signal === 'SELL') {
                signalElement.className = 'text-2xl font-bold text-red-600';
            } else {
                signalElement.className = 'text-2xl font-bold text-yellow-600';
            }
        }

        // Display key insights
        function displayKeyInsights(insights) {
            const section = document.getElementById('key-insights-section');
            const container = document.getElementById('key-insights');

            if (!insights || insights.length === 0) return;

            section.classList.remove('hidden');

            // Parse insights if it's a JSON string
            let parsedInsights = insights;
            if (typeof insights === 'string') {
                try {
                    parsedInsights = JSON.parse(insights);
                } catch (e) {
                    parsedInsights = [insights];
                }
            }

            container.innerHTML = parsedInsights.map(insight => 
                `<li class="flex items-start space-x-2">
                    <span class="text-blue-600 font-bold">•</span>
                    <span class="text-gray-700">${insight}</span>
                </li>`
            ).join('');
        }

        // Display price impact
        function displayPriceImpact(article) {
            const section = document.getElementById('price-impact-section');
            section.classList.remove('hidden');

            const shortImpact = article.price_impact_short || 0;
            const longImpact = article.price_impact_long || 0;

            document.getElementById('price-impact-short').textContent = `${shortImpact > 0 ? '+' : ''}${shortImpact.toFixed(2)}%`;
            document.getElementById('price-impact-long').textContent = `${longImpact > 0 ? '+' : ''}${longImpact.toFixed(2)}%`;

            // Color code the impacts
            const shortElement = document.getElementById('price-impact-short');
            const longElement = document.getElementById('price-impact-long');

            shortElement.className = `text-lg font-bold ${shortImpact >= 0 ? 'text-green-600' : 'text-red-600'}`;
            longElement.className = `text-lg font-bold ${longImpact >= 0 ? 'text-green-600' : 'text-red-600'}`;
        }

        // Load related articles
        async function loadRelatedArticles(sentiment) {
            try {
                const response = await PulseChainAPI.getArticles({ limit: 5, sentiment });
                const container = document.getElementById('related-articles');

                if (response.success && response.data.length > 0) {
                    const relatedHtml = response.data.map(article => `
                        <div class="border-l-4 border-blue-600 pl-4 py-2">
                            <h4 class="font-semibold text-gray-900 mb-1">
                                <a href="/analysis?id=${article.id}" class="hover:text-blue-600">
                                    ${article.title}
                                </a>
                            </h4>
                            <p class="text-sm text-gray-600">${article.source_name} • ${new Date(article.published_at).toLocaleDateString()}</p>
                        </div>
                    `).join('');
                    container.innerHTML = relatedHtml;
                } else {
                    container.innerHTML = '<p class="text-gray-600">No related articles found</p>';
                }
            } catch (error) {
                console.error('Related articles error:', error);
                document.getElementById('related-articles').innerHTML = '<p class="text-gray-600">Failed to load related articles</p>';
            }
        }

        // Show loading state
        function showLoading() {
            loadingState.classList.remove('hidden');
            errorState.classList.add('hidden');
            articleContent.classList.add('hidden');
        }

        // Hide loading state
        function hideLoading() {
            loadingState.classList.add('hidden');
            articleContent.classList.remove('hidden');
        }

        // Show error state
        function showError(message) {
            errorMessage.textContent = message;
            loadingState.classList.add('hidden');
            errorState.classList.remove('hidden');
            articleContent.classList.add('hidden');
        }
    </script>
</body>
</html>