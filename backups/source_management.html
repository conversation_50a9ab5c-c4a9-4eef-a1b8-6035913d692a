<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Source Management - PulseChain News Hub</title>
    <link rel="stylesheet" href="/css/main.css">
    <link rel="stylesheet" href="/css/tailwind.css">
    <style>
        .loading-spinner {
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        .source-card {
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        .source-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <h1 class="text-2xl font-bold text-gray-900">📰 PulseChain News Hub</h1>
                </div>
                <nav class="flex space-x-4">
                    <a href="/dashboard" class="text-gray-600 hover:text-blue-600 px-4 py-2 rounded-md">Dashboard</a>
                    <a href="/analysis" class="text-gray-600 hover:text-blue-600 px-4 py-2 rounded-md">Analysis</a>
                    <a href="/sources" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">Sources</a>
                    <button id="refresh-btn" class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700">
                        Refresh Sources
                    </button>
                </nav>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Page Header -->
        <div class="mb-8">
            <h2 class="text-3xl font-bold text-gray-900 mb-2">News Sources</h2>
            <p class="text-gray-600">Manage and monitor your PulseChain news sources</p>
        </div>

        <!-- Stats Summary -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow p-6">
                <div class="text-center">
                    <div class="text-2xl font-bold text-blue-600" id="total-sources">Loading...</div>
                    <div class="text-sm text-gray-600">Total Sources</div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow p-6">
                <div class="text-center">
                    <div class="text-2xl font-bold text-green-600" id="active-sources">Loading...</div>
                    <div class="text-sm text-gray-600">Active Sources</div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow p-6">
                <div class="text-center">
                    <div class="text-2xl font-bold text-yellow-600" id="avg-credibility">Loading...</div>
                    <div class="text-sm text-gray-600">Avg. Credibility</div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow p-6">
                <div class="text-center">
                    <div class="text-2xl font-bold text-purple-600" id="total-articles">Loading...</div>
                    <div class="text-sm text-gray-600">Articles Scraped</div>
                </div>
            </div>
        </div>

        <!-- Sources List -->
        <div class="bg-white rounded-lg shadow">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Source Status</h3>
            </div>
            
            <!-- Loading State -->
            <div id="loading-state" class="text-center py-12">
                <div class="loading-spinner inline-block w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full"></div>
                <p class="mt-2 text-gray-600">Loading sources...</p>
            </div>

            <!-- Sources Container -->
            <div id="sources-container" class="hidden">
                <!-- Sources will be populated here -->
            </div>

            <!-- Error State -->
            <div id="error-state" class="hidden p-6 text-center">
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                    <h3 class="font-bold">Error</h3>
                    <p id="error-message">Failed to load sources</p>
                    <button onclick="loadSources()" class="mt-2 bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700">
                        Try Again
                    </button>
                </div>
            </div>
        </div>
    </main>

    <!-- Include API Client -->
    <script src="/js/api.js"></script>
    
    <script>
        // DOM elements
        const loadingState = document.getElementById('loading-state');
        const sourcesContainer = document.getElementById('sources-container');
        const errorState = document.getElementById('error-state');
        const errorMessage = document.getElementById('error-message');
        const refreshBtn = document.getElementById('refresh-btn');

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Sources page initializing...');
            loadSources();
            setupEventListeners();
        });

        // Setup event listeners
        function setupEventListeners() {
            refreshBtn.addEventListener('click', loadSources);
        }

        // Load sources data
        async function loadSources() {
            try {
                console.log('Loading sources...');
                showLoading();
                
                const response = await PulseChainAPI.getSources();

                if (response.success) {
                    displaySources(response.data);
                    updateStats(response.data);
                    hideLoading();
                } else {
                    throw new Error('Failed to load sources');
                }

            } catch (error) {
                console.error('Sources loading error:', error);
                showError('Failed to load sources: ' + error.message);
            }
        }

        // Display sources
        function displaySources(sources) {
            if (!sources || sources.length === 0) {
                sourcesContainer.innerHTML = `
                    <div class="p-6 text-center">
                        <p class="text-gray-600">No sources available</p>
                    </div>
                `;
                return;
            }

            const sourceElements = sources.map(source => createSourceElement(source)).join('');
            sourcesContainer.innerHTML = sourceElements;
        }

        // Create source element
        function createSourceElement(source) {
            // Format last scraped date
            const lastScraped = source.last_scraped_at 
                ? new Date(source.last_scraped_at).toLocaleString()
                : 'Never';

            // Get status color
            const isActive = source.is_active;
            const statusColor = isActive ? 'text-green-600 bg-green-100' : 'text-red-600 bg-red-100';
            const statusText = isActive ? 'Active' : 'Inactive';

            // Get credibility color
            const credibility = parseFloat(source.credibility_score) || 0;
            let credibilityColor = 'text-gray-600';
            if (credibility >= 8) credibilityColor = 'text-green-600';
            else if (credibility >= 6) credibilityColor = 'text-yellow-600';
            else if (credibility >= 4) credibilityColor = 'text-orange-600';
            else credibilityColor = 'text-red-600';

            return `
                <div class="source-card border-b border-gray-200 p-6">
                    <div class="flex justify-between items-start">
                        <div class="flex-1">
                            <div class="flex items-center justify-between mb-2">
                                <h4 class="text-lg font-semibold text-gray-900">${source.name}</h4>
                                <span class="inline-flex px-2 py-1 text-xs font-medium rounded-full ${statusColor}">
                                    ${statusText}
                                </span>
                            </div>
                            
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                                <div>
                                    <span class="text-gray-600">Type: </span>
                                    <span class="font-medium capitalize">${source.type}</span>
                                </div>
                                <div>
                                    <span class="text-gray-600">Credibility: </span>
                                    <span class="font-medium ${credibilityColor}">${credibility.toFixed(1)}/10</span>
                                </div>
                                <div>
                                    <span class="text-gray-600">Articles: </span>
                                    <span class="font-medium">${source.total_articles || 0}</span>
                                </div>
                            </div>
                            
                            <div class="mt-2 text-sm">
                                <span class="text-gray-600">Last Scraped: </span>
                                <span class="font-medium">${lastScraped}</span>
                            </div>
                            
                            <div class="mt-2">
                                <a href="${source.url}" target="_blank" class="text-blue-600 hover:text-blue-800 text-sm">
                                    ${source.url}
                                    <svg class="inline w-3 h-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"/>
                                    </svg>
                                </a>
                            </div>

                            ${source.tags ? `
                            <div class="mt-3">
                                <div class="flex flex-wrap gap-1">
                                    ${JSON.parse(source.tags).map(tag => 
                                        `<span class="inline-flex px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded">${tag}</span>`
                                    ).join('')}
                                </div>
                            </div>
                            ` : ''}
                        </div>
                        
                        <div class="ml-4 flex flex-col space-y-2">
                            <button onclick="viewSourceArticles(${source.id})" 
                                    class="bg-blue-600 text-white px-3 py-1 text-xs rounded hover:bg-blue-700">
                                View Articles
                            </button>
                            ${source.success_rate !== undefined ? `
                            <div class="text-xs text-center">
                                <div class="text-gray-600">Success Rate</div>
                                <div class="font-bold ${source.success_rate > 0.8 ? 'text-green-600' : source.success_rate > 0.5 ? 'text-yellow-600' : 'text-red-600'}">
                                    ${Math.round(source.success_rate * 100)}%
                                </div>
                            </div>
                            ` : ''}
                        </div>
                    </div>
                </div>
            `;
        }

        // Update stats
        function updateStats(sources) {
            const totalSources = sources.length;
            const activeSources = sources.filter(s => s.is_active).length;
            const avgCredibility = sources.reduce((sum, s) => sum + (parseFloat(s.credibility_score) || 0), 0) / totalSources;
            const totalArticles = sources.reduce((sum, s) => sum + (s.total_articles || 0), 0);

            document.getElementById('total-sources').textContent = totalSources;
            document.getElementById('active-sources').textContent = activeSources;
            document.getElementById('avg-credibility').textContent = avgCredibility.toFixed(1);
            document.getElementById('total-articles').textContent = totalArticles;
        }

        // View source articles
        function viewSourceArticles(sourceId) {
            // For now, redirect to dashboard with source filter
            // In a more advanced version, you could filter by source
            window.location.href = '/dashboard';
        }

        // Show loading state
        function showLoading() {
            loadingState.classList.remove('hidden');
            sourcesContainer.classList.add('hidden');
            errorState.classList.add('hidden');
        }

        // Hide loading state
        function hideLoading() {
            loadingState.classList.add('hidden');
            sourcesContainer.classList.remove('hidden');
        }

        // Show error state
        function showError(message) {
            errorMessage.textContent = message;
            loadingState.classList.add('hidden');
            sourcesContainer.classList.add('hidden');
            errorState.classList.remove('hidden');
        }
    </script>
</body>
</html>