@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Source+Sans+3:wght@400;500&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Primary Colors */
  --color-primary: #1E3A8A; /* blue-800 */
  --color-primary-50: #EFF6FF; /* blue-50 */
  --color-primary-100: #DBEAFE; /* blue-100 */
  --color-primary-500: #3B82F6; /* blue-500 */
  --color-primary-600: #2563EB; /* blue-600 */
  --color-primary-700: #1D4ED8; /* blue-700 */
  --color-primary-800: #1E40AF; /* blue-800 */
  --color-primary-900: #1E3A8A; /* blue-900 */

  /* Secondary Colors */
  --color-secondary: #64748B; /* slate-500 */
  --color-secondary-50: #F8FAFC; /* slate-50 */
  --color-secondary-100: #F1F5F9; /* slate-100 */
  --color-secondary-200: #E2E8F0; /* slate-200 */
  --color-secondary-300: #CBD5E1; /* slate-300 */
  --color-secondary-400: #94A3B8; /* slate-400 */
  --color-secondary-500: #64748B; /* slate-500 */
  --color-secondary-600: #475569; /* slate-600 */
  --color-secondary-700: #334155; /* slate-700 */

  /* Accent Colors */
  --color-accent: #F59E0B; /* amber-500 */
  --color-accent-50: #FFFBEB; /* amber-50 */
  --color-accent-100: #FEF3C7; /* amber-100 */
  --color-accent-200: #FDE68A; /* amber-200 */
  --color-accent-500: #F59E0B; /* amber-500 */
  --color-accent-600: #D97706; /* amber-600 */

  /* Background Colors */
  --color-background: #FAFBFC; /* custom off-white */
  --color-surface: #FFFFFF; /* white */

  /* Text Colors */
  --color-text-primary: #1F2937; /* gray-800 */
  --color-text-secondary: #6B7280; /* gray-500 */

  /* Status Colors */
  --color-success: #10B981; /* emerald-500 */
  --color-success-50: #ECFDF5; /* emerald-50 */
  --color-success-100: #D1FAE5; /* emerald-100 */
  --color-success-500: #10B981; /* emerald-500 */
  --color-success-600: #059669; /* emerald-600 */

  --color-warning: #F59E0B; /* amber-500 */
  --color-warning-50: #FFFBEB; /* amber-50 */
  --color-warning-100: #FEF3C7; /* amber-100 */
  --color-warning-500: #F59E0B; /* amber-500 */
  --color-warning-600: #D97706; /* amber-600 */

  --color-error: #EF4444; /* red-500 */
  --color-error-50: #FEF2F2; /* red-50 */
  --color-error-100: #FEE2E2; /* red-100 */
  --color-error-500: #EF4444; /* red-500 */
  --color-error-600: #DC2626; /* red-600 */

  /* Border Colors */
  --color-border: #E5E7EB; /* gray-200 */
  --color-border-light: #F3F4F6; /* gray-100 */

  /* Shadow Variables */
  --shadow-card: 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-floating: 0 4px 12px rgba(0, 0, 0, 0.15);
  --shadow-hover: 0 2px 8px rgba(0, 0, 0, 0.12);

  /* Animation Variables */
  --transition-fast: 200ms ease-out;
  --transition-normal: 300ms ease-out;
  --transition-height: height 0.3s ease-out;
}

/* Base Styles */
@layer base {
  body {
    font-family: 'Source Sans 3', sans-serif;
    background-color: var(--color-background);
    color: var(--color-text-primary);
    line-height: 1.6;
  }

  h1, h2, h3, h4, h5, h6 {
    font-family: 'Inter', sans-serif;
    font-weight: 600;
    line-height: 1.3;
  }

  .font-data {
    font-family: 'JetBrains Mono', monospace;
  }
}

/* Component Styles */
@layer components {
  /* Card Components */
  .card {
    background-color: var(--color-surface);
    border-radius: 0.5rem;
    box-shadow: var(--shadow-card);
    border: 1px solid var(--color-border-light);
    transition: box-shadow var(--transition-normal), transform var(--transition-fast);
  }

  .card:hover {
    box-shadow: var(--shadow-hover);
    transform: translateY(-1px);
  }

  .card-floating {
    box-shadow: var(--shadow-floating);
  }

  /* Credibility Indicators */
  .credibility-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    font-size: 0.75rem;
    font-weight: 500;
    transition: transform var(--transition-fast), opacity var(--transition-fast);
  }

  .credibility-badge:hover {
    transform: scale(1.02);
    opacity: 0.9;
  }

  .credibility-verified {
    background-color: var(--color-success-100);
    color: var(--color-success-600);
  }

  .credibility-warning {
    background-color: var(--color-warning-100);
    color: var(--color-warning-600);
  }

  .credibility-error {
    background-color: var(--color-error-100);
    color: var(--color-error-600);
  }

  /* Expandable Content */
  .expandable-content {
    overflow: hidden;
    transition: var(--transition-height);
  }

  /* Loading States */
  .skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
  }

  @keyframes shimmer {
    0% {
      background-position: -200% 0;
    }
    100% {
      background-position: 200% 0;
    }
  }

  /* Notification Styles */
  .notification-toast {
    position: fixed;
    top: 1rem;
    right: 1rem;
    z-index: 50;
    animation: slideInFromRight 0.3s ease-out;
  }

  @keyframes slideInFromRight {
    from {
      transform: translateX(100%);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }

  /* Button Styles */
  .btn-primary {
    background-color: var(--color-primary);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    font-weight: 500;
    transition: background-color var(--transition-fast), transform var(--transition-fast);
  }

  .btn-primary:hover {
    background-color: var(--color-primary-700);
    transform: translateY(-1px);
  }

  .btn-secondary {
    background-color: var(--color-secondary-100);
    color: var(--color-secondary-700);
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    font-weight: 500;
    transition: background-color var(--transition-fast), transform var(--transition-fast);
  }

  .btn-secondary:hover {
    background-color: var(--color-secondary-200);
    transform: translateY(-1px);
  }

  /* Border Accents */
  .border-accent-left {
    border-left: 3px solid var(--color-primary);
  }

  .border-accent-success {
    border-left: 3px solid var(--color-success);
  }

  .border-accent-warning {
    border-left: 3px solid var(--color-warning);
  }

  .border-accent-error {
    border-left: 3px solid var(--color-error);
  }

  /* Scroll Snap for Mobile */
  .scroll-snap-x {
    scroll-snap-type: x mandatory;
    -webkit-overflow-scrolling: touch;
  }

  .scroll-snap-item {
    scroll-snap-align: start;
  }
}

/* Utility Classes */
@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  .transition-height {
    transition: var(--transition-height);
  }

  .shadow-card {
    box-shadow: var(--shadow-card);
  }

  .shadow-floating {
    box-shadow: var(--shadow-floating);
  }

  .shadow-hover {
    box-shadow: var(--shadow-hover);
  }
}