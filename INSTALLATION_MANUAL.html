<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PulseChain News Hub - Installation & Configuration Manual</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f7fa;
            color: #333;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5em;
        }
        
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }
        
        .section {
            background: white;
            padding: 30px;
            margin: 20px 0;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .section h2 {
            color: #667eea;
            border-bottom: 3px solid #667eea;
            padding-bottom: 10px;
            margin-top: 0;
        }
        
        .section h3 {
            color: #764ba2;
            margin-top: 25px;
        }
        
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 15px 0;
            border-left: 4px solid #667eea;
        }
        
        .inline-code {
            background: #e2e8f0;
            color: #2d3748;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 0.9em;
        }
        
        .warning {
            background: #fed7d7;
            border: 1px solid #fc8181;
            color: #742a2a;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        
        .success {
            background: #c6f6d5;
            border: 1px solid #68d391;
            color: #22543d;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        
        .info {
            background: #bee3f8;
            border: 1px solid #63b3ed;
            color: #2a4365;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        
        .step {
            background: #f7fafc;
            border-left: 4px solid #667eea;
            padding: 15px;
            margin: 15px 0;
        }
        
        .file-path {
            background: #edf2f7;
            color: #4a5568;
            padding: 8px 12px;
            border-radius: 6px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 0.9em;
            display: inline-block;
            margin: 5px 0;
        }
        
        .status-check {
            background: #f0fff4;
            border: 2px solid #48bb78;
            padding: 10px;
            border-radius: 6px;
            margin: 10px 0;
        }
        
        .command-output {
            background: #1a202c;
            color: #68d391;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 13px;
            margin: 10px 0;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        table th, table td {
            border: 1px solid #e2e8f0;
            padding: 12px;
            text-align: left;
        }
        
        table th {
            background: #667eea;
            color: white;
            font-weight: 600;
        }
        
        table tr:nth-child(even) {
            background: #f8f9fa;
        }
        
        .nav {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .nav ul {
            list-style: none;
            padding: 0;
            margin: 0;
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
        }
        
        .nav a {
            color: #667eea;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 6px;
            background: #edf2f7;
            transition: all 0.3s;
        }
        
        .nav a:hover {
            background: #667eea;
            color: white;
        }
        
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .section {
                padding: 20px;
            }
            
            .nav ul {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 PulseChain News Hub</h1>
        <p>Complete Installation & Configuration Manual for Ubuntu/Linux Mint</p>
        <p><strong>AI-Powered News Aggregator with DeepSeek Integration</strong></p>
    </div>

    <nav class="nav">
        <ul>
            <li><a href="#prerequisites">📋 Prerequisites</a></li>
            <li><a href="#installation">🔧 Installation</a></li>
            <li><a href="#configuration">⚙️ Configuration</a></li>
            <li><a href="#server-management">🖥️ Server Management</a></li>
            <li><a href="#api-setup">🔗 API Setup</a></li>
            <li><a href="#dex-config">💹 DEX Configuration</a></li>
            <li><a href="#troubleshooting">🛠️ Troubleshooting</a></li>
            <li><a href="#maintenance">🔄 Maintenance</a></li>
        </ul>
    </nav>

    <section id="prerequisites" class="section">
        <h2>📋 Prerequisites & System Requirements</h2>
        
        <h3>System Requirements</h3>
        <ul>
            <li><strong>OS:</strong> Ubuntu 18.04+ or Linux Mint 19+</li>
            <li><strong>RAM:</strong> Minimum 2GB, Recommended 4GB+</li>
            <li><strong>Storage:</strong> 1GB free space</li>
            <li><strong>Network:</strong> Stable internet connection</li>
            <li><strong>Node.js:</strong> Version 18.0.0 or higher</li>
        </ul>

        <h3>Required Dependencies</h3>
        <div class="step">
            <h4>Step 1: Update System Packages</h4>
            <div class="code-block">sudo apt update && sudo apt upgrade -y</div>
        </div>

        <div class="step">
            <h4>Step 2: Install Essential Build Tools</h4>
            <div class="code-block">sudo apt install -y curl wget git build-essential python3-dev python3-distutils</div>
        </div>

        <div class="step">
            <h4>Step 3: Install Node.js via NodeSource</h4>
            <div class="code-block">curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
sudo apt install -y nodejs</div>
        </div>

        <div class="status-check">
            <strong>✅ Verify Installation:</strong>
            <div class="code-block">node --version
npm --version</div>
            <div class="command-output">v20.x.x
10.x.x</div>
        </div>
    </section>

    <section id="installation" class="section">
        <h2>🔧 Installation Process</h2>

        <h3>Method 1: Fresh Installation</h3>
        
        <div class="step">
            <h4>Step 1: Clone or Download Project</h4>
            <div class="code-block"># Option A: If you have the project files
cd /path/to/your/pulsechain_news_hub

# Option B: If cloning from repository
git clone [repository-url] pulsechain_news_hub
cd pulsechain_news_hub</div>
        </div>

        <div class="step">
            <h4>Step 2: Install Node.js Dependencies</h4>
            <div class="code-block">npm install</div>
            
            <div class="info">
                <strong>Note:</strong> If you encounter permission errors, use:
                <div class="inline-code">npm install --no-optional</div>
            </div>
        </div>

        <div class="step">
            <h4>Step 3: Create Environment Configuration</h4>
            <div class="code-block">cp .env.example .env
# Or create new .env file with your settings</div>
        </div>

        <div class="step">
            <h4>Step 4: Set Up API Keys</h4>
            <p>Edit the <span class="file-path">.env</span> file:</p>
            <div class="code-block">nano .env</div>
            
            <p>Add your DeepSeek API key:</p>
            <div class="code-block"># DeepSeek API Configuration
DEEPSEEK_API_KEY=your_deepseek_api_key_here
DEEPSEEK_BASE_URL=https://api.deepseek.com

# Server Configuration
PORT=3001
NODE_ENV=development

# Database Configuration
DB_PATH=./data/pulsechain_news.db</div>
        </div>

        <h3>Method 2: Quick Setup Script</h3>
        
        <div class="step">
            <h4>Create Auto-Install Script</h4>
            <div class="code-block">cat << 'EOF' > install.sh
#!/bin/bash
echo "🚀 Installing PulseChain News Hub..."

# Update system
sudo apt update

# Install dependencies
sudo apt install -y curl wget git build-essential python3-dev python3-distutils

# Install Node.js
curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
sudo apt install -y nodejs

# Install npm dependencies
npm install

# Create data directory
mkdir -p data

echo "✅ Installation completed!"
echo "📝 Please configure your .env file before starting"
EOF

chmod +x install.sh
./install.sh</div>
        </div>
    </section>

    <section id="configuration" class="section">
        <h2>⚙️ Configuration Guide</h2>

        <h3>Environment Variables (.env)</h3>
        <p>Complete <span class="file-path">.env</span> configuration:</p>
        
        <div class="code-block"># DeepSeek AI Configuration
DEEPSEEK_API_KEY=sk-your-deepseek-api-key-here
DEEPSEEK_BASE_URL=https://api.deepseek.com

# Server Configuration
PORT=3001
NODE_ENV=development

# WebSocket Configuration
WS_PORT=3002

# Database Configuration
DB_PATH=./data/pulsechain_news.db

# News Scraping Configuration
SCRAPING_INTERVAL=300000
MAX_ARTICLES_PER_SOURCE=50

# DEX Data Configuration
PULSECHAIN_RPC=https://rpc.pulsechain.com
PULSEX_API=https://api.pulsex.com/api/v1
DEX_REFRESH_INTERVAL=60000

# Rate Limiting
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=100</div>

        <div class="warning">
            <strong>⚠️ Security Notice:</strong> Never commit your <span class="inline-code">.env</span> file to version control. 
            Keep your API keys private and rotate them regularly.
        </div>

        <h3>Directory Structure</h3>
        <div class="code-block">pulsechain_news_hub/
├── .env                    # Environment variables
├── server.js              # Main server file
├── package.json           # Dependencies
├── data/                  # Database storage
├── src/
│   ├── database/          # Database schemas & models
│   ├── services/          # Core services
│   │   ├── deepseek.js   # AI integration
│   │   ├── scraper.js    # News scraping
│   │   ├── dex.js        # DEX data
│   │   └── analyzer.js   # Analysis pipeline
├── pages/                 # Frontend HTML pages
├── js/                    # Frontend JavaScript
├── css/                   # Stylesheets
└── public/                # Static assets</div>
    </section>

    <section id="server-management" class="section">
        <h2>🖥️ Server Management Commands</h2>

        <h3>Starting the Server</h3>
        
        <div class="step">
            <h4>Development Mode (with auto-restart)</h4>
            <div class="code-block">npm run dev</div>
        </div>

        <div class="step">
            <h4>Production Mode</h4>
            <div class="code-block">npm start</div>
        </div>

        <div class="step">
            <h4>Background Process (Linux/Unix)</h4>
            <div class="code-block"># Start in background
nohup npm start > server.log 2>&1 &

# Check if running
ps aux | grep node

# View logs
tail -f server.log</div>
        </div>

        <h3>Stopping the Server</h3>
        
        <div class="step">
            <h4>Graceful Shutdown</h4>
            <div class="code-block"># If running in terminal: Ctrl+C

# If running in background:
ps aux | grep node
kill [process_id]

# Force kill if needed:
pkill -f "node server.js"</div>
        </div>

        <h3>Server Status & Health Checks</h3>
        
        <div class="step">
            <h4>Quick Health Check</h4>
            <div class="code-block">curl http://localhost:3001/api/health | jq '.'</div>
            
            <div class="command-output">{
  "status": "healthy",
  "services": {
    "database": {"success": true},
    "deepseek": {"success": true},
    "analyzer": {"isRunning": false}
  },
  "uptime": 123.45
}</div>
        </div>

        <div class="step">
            <h4>Test All Connections</h4>
            <div class="code-block">npm test</div>
        </div>

        <h3>Access URLs</h3>
        <table>
            <tr><th>Service</th><th>URL</th><th>Description</th></tr>
            <tr><td>Main Dashboard</td><td><span class="inline-code">http://localhost:3001/dashboard</span></td><td>News dashboard interface</td></tr>
            <tr><td>Article Analysis</td><td><span class="inline-code">http://localhost:3001/analysis</span></td><td>AI analysis interface</td></tr>
            <tr><td>Source Management</td><td><span class="inline-code">http://localhost:3001/sources</span></td><td>News source configuration</td></tr>
            <tr><td>API Health</td><td><span class="inline-code">http://localhost:3001/api/health</span></td><td>System health status</td></tr>
            <tr><td>API Articles</td><td><span class="inline-code">http://localhost:3001/api/articles</span></td><td>Articles REST API</td></tr>
            <tr><td>DEX Data API</td><td><span class="inline-code">http://localhost:3001/api/dex/current</span></td><td>Live DEX data</td></tr>
            <tr><td>Trading Signals</td><td><span class="inline-code">http://localhost:3001/api/signals</span></td><td>AI trading signals</td></tr>
        </table>
    </section>

    <section id="api-setup" class="section">
        <h2>🔗 API Keys & External Services</h2>

        <h3>DeepSeek AI Setup</h3>
        
        <div class="step">
            <h4>Step 1: Create DeepSeek Account</h4>
            <ol>
                <li>Visit <a href="https://platform.deepseek.com" target="_blank">https://platform.deepseek.com</a></li>
                <li>Sign up for an account</li>
                <li>Navigate to API Keys section</li>
                <li>Create a new API key</li>
                <li>Copy the key (starts with <span class="inline-code">sk-</span>)</li>
            </ol>
        </div>

        <div class="step">
            <h4>Step 2: Configure DeepSeek in .env</h4>
            <div class="code-block">DEEPSEEK_API_KEY=sk-your-actual-api-key-here</div>
        </div>

        <div class="step">
            <h4>Step 3: Test DeepSeek Connection</h4>
            <div class="code-block">curl -X POST "http://localhost:3001/api/health" | jq '.services.deepseek'</div>
        </div>

        <h3>Adding New API Keys</h3>
        
        <div class="step">
            <h4>Edit Configuration File</h4>
            <p>To add new API services, edit <span class="file-path">src/services/</span> files:</p>
            
            <div class="code-block"># For new DEX APIs - edit:
nano src/services/dex.js

# For new news sources - edit:
nano src/services/scraper.js

# For new AI services - edit:
nano src/services/deepseek.js</div>
        </div>

        <div class="step">
            <h4>Example: Adding CoinGecko API</h4>
            <p>Edit <span class="file-path">src/services/dex.js</span>:</p>
            <div class="code-block">// Add to constructor
this.coingeckoAPI = process.env.COINGECKO_API_KEY;

// Add to .env file
COINGECKO_API_KEY=your_coingecko_api_key</div>
        </div>

        <h3>API Rate Limits & Costs</h3>
        <table>
            <tr><th>Service</th><th>Free Tier</th><th>Rate Limit</th><th>Cost</th></tr>
            <tr><td>DeepSeek AI</td><td>$5 credit</td><td>Varies by model</td><td>~$0.001-0.01/request</td></tr>
            <tr><td>DexScreener</td><td>✅ Free</td><td>60 req/min</td><td>Free</td></tr>
            <tr><td>CoinGecko</td><td>50 calls/min</td><td>50/min</td><td>Free/Paid tiers</td></tr>
            <tr><td>RSS Feeds</td><td>✅ Free</td><td>Varies</td><td>Free</td></tr>
        </table>
    </section>

    <section id="dex-config" class="section">
        <h2>💹 DEX & Price Data Configuration</h2>

        <h3>Current DEX Integrations</h3>
        <p>Edit <span class="file-path">src/services/dex.js</span> to modify DEX data sources:</p>

        <div class="step">
            <h4>Supported Tokens (Default)</h4>
            <div class="code-block">this.pulseChainTokens = {
    PLS: {
        symbol: 'PLS',
        name: 'PulseChain',
        address: '0x0000000000000000000000000000000000000000',
        decimals: 18
    },
    PLSX: {
        symbol: 'PLSX',
        name: 'PulseX',
        address: '0x95B303987A60C71504D99Aa1b13B4DA07b0790ab',
        decimals: 18
    },
    HEX: {
        symbol: 'HEX',
        name: 'HEX',
        address: '0x2b591e99afe9f32eaa6214f7b7629768c40eeb39',
        decimals: 8
    },
    PHEX: {
        symbol: 'PHEX',
        name: 'Pulse HEX',
        address: '0x2b591e99afe9f32eaa6214f7b7629768c40eeb39',
        decimals: 8
    }
};</div>
        </div>

        <div class="step">
            <h4>Adding New Tokens</h4>
            <p>To add support for additional tokens:</p>
            <div class="code-block">// Add to pulseChainTokens object in src/services/dex.js
NEWTOK: {
    symbol: 'NEWTOK',
    name: 'New Token Name',
    address: '0x...token_contract_address...',
    decimals: 18
}</div>
        </div>

        <h3>Price Data Sources</h3>
        
        <div class="step">
            <h4>Current APIs Used</h4>
            <div class="code-block">this.endpoints = {
    coingecko: 'https://api.coingecko.com/api/v3',
    dexscreener: 'https://api.dexscreener.com/latest/dex',
    moralis: 'https://deep-index.moralis.io/api/v2'
};</div>
        </div>

        <div class="step">
            <h4>Adding New DEX APIs</h4>
            <p>To add new price data sources:</p>
            <div class="code-block">// Add to endpoints object
newapi: 'https://api.newdex.com/v1',

// Add to .env file
NEWAPI_KEY=your_api_key_here

// Implement in getPriceData() method
const newApiData = await axios.get(
    `${this.endpoints.newapi}/prices?token=PLS`,
    { headers: { 'Authorization': `Bearer ${process.env.NEWAPI_KEY}` }}
);</div>
        </div>

        <h3>Market Health Calculation</h3>
        <p>The system calculates market health based on:</p>
        <ul>
            <li><strong>Volume Trend (30%):</strong> 24h volume change</li>
            <li><strong>Price Stability (25%):</strong> Volatility measure</li>
            <li><strong>Liquidity Health (25%):</strong> Liquidity pool changes</li>
            <li><strong>Trading Activity (20%):</strong> Transaction volume</li>
        </ul>

        <div class="info">
            <strong>Customize Weights:</strong> Edit the <span class="inline-code">calculateMarketHealth()</span> 
            function in <span class="file-path">src/services/dex.js</span> to adjust scoring criteria.
        </div>
    </section>

    <section id="troubleshooting" class="section">
        <h2>🛠️ Troubleshooting Common Issues</h2>

        <h3>Issue: "Cannot GET /pages/news_dashboard.html"</h3>
        <div class="warning">
            <strong>Cause:</strong> Incorrect URL or server routing issue.
            <br><strong>Solution:</strong>
        </div>
        <div class="code-block"># Use correct URLs:
http://localhost:3001/dashboard          ✅ Correct
http://localhost:3001/pages/news_dashboard.html  ❌ Wrong

# Check if server is running:
curl http://localhost:3001/api/health</div>

        <h3>Issue: "SQL Error: no such column: now"</h3>
        <div class="warning">
            <strong>Cause:</strong> SQLite datetime function syntax issue.
            <br><strong>Solution:</strong>
        </div>
        <div class="code-block"># Delete existing database to recreate:
rm -f data/pulsechain_news.db

# Restart server:
npm start</div>

        <h3>Issue: "Request timed out after 10000ms"</h3>
        <div class="info">
            <strong>Cause:</strong> Network connectivity or API rate limiting.
            <br><strong>Solution:</strong> This is normal - the system has built-in retries.
        </div>

        <h3>Issue: DeepSeek API Timeouts</h3>
        <div class="step">
            <h4>Check API Key</h4>
            <div class="code-block"># Verify API key format:
echo $DEEPSEEK_API_KEY
# Should start with 'sk-'

# Test directly:
curl -H "Authorization: Bearer $DEEPSEEK_API_KEY" \
     -H "Content-Type: application/json" \
     -d '{"model":"deepseek-chat","messages":[{"role":"user","content":"test"}]}' \
     https://api.deepseek.com/v1/chat/completions</div>
        </div>

        <h3>Issue: Permission Errors</h3>
        <div class="step">
            <h4>Fix File Permissions</h4>
            <div class="code-block"># Fix project permissions:
sudo chown -R $USER:$USER .
chmod -R 755 .

# Fix npm permissions:
npm config set prefix '~/.npm-global'
echo 'export PATH=~/.npm-global/bin:$PATH' >> ~/.bashrc
source ~/.bashrc</div>
        </div>

        <h3>Issue: Port Already in Use</h3>
        <div class="step">
            <h4>Find and Kill Process</h4>
            <div class="code-block"># Find process using port 3001:
sudo lsof -i :3001

# Kill process:
kill -9 [PID]

# Or use different port in .env:
PORT=3005</div>
        </div>

        <h3>Debug Mode</h3>
        <div class="step">
            <h4>Enable Detailed Logging</h4>
            <div class="code-block"># Add to .env:
NODE_ENV=development
DEBUG=*

# Run with debug output:
DEBUG=* npm start</div>
        </div>
    </section>

    <section id="maintenance" class="section">
        <h2>🔄 Maintenance & Updates</h2>

        <h3>Checking for Updates</h3>
        
        <div class="step">
            <h4>Check Outdated Packages</h4>
            <div class="code-block">npm outdated</div>
            
            <div class="command-output">Package                    Current   Wanted   Latest
express                     4.18.2   4.18.2   4.19.2
axios                       1.6.0    1.6.0    1.6.2
better-sqlite3              9.2.2    9.2.2    9.4.0</div>
        </div>

        <div class="step">
            <h4>Update Dependencies</h4>
            <div class="code-block"># Update all packages:
npm update

# Update specific package:
npm install express@latest

# Check for security vulnerabilities:
npm audit

# Fix vulnerabilities:
npm audit fix</div>
        </div>

        <h3>Database Maintenance</h3>
        
        <div class="step">
            <h4>Database Cleanup</h4>
            <div class="code-block"># Via API (automated):
curl -X POST http://localhost:3001/api/analysis/cleanup

# Manual cleanup (removes data older than 30 days):
rm -f data/old_backup.db
cp data/pulsechain_news.db data/backup_$(date +%Y%m%d).db</div>
        </div>

        <div class="step">
            <h4>Log Rotation</h4>
            <div class="code-block"># Create log rotation script:
cat << 'EOF' > rotate_logs.sh
#!/bin/bash
if [ -f server.log ]; then
    mv server.log server_$(date +%Y%m%d_%H%M%S).log
    touch server.log
fi
EOF

chmod +x rotate_logs.sh

# Add to crontab for daily rotation:
crontab -e
# Add: 0 2 * * * /path/to/rotate_logs.sh</div>
        </div>

        <h3>Performance Monitoring</h3>
        
        <div class="step">
            <h4>System Resource Usage</h4>
            <div class="code-block"># Monitor CPU and memory:
htop

# Monitor disk usage:
df -h

# Monitor network connections:
netstat -tulpn | grep :3001</div>
        </div>

        <div class="step">
            <h4>API Performance Metrics</h4>
            <div class="code-block"># Get system metrics:
curl http://localhost:3001/api/health | jq '.memory'

# Check database stats:
curl http://localhost:3001/api/analytics | jq '.data.stats'</div>
        </div>

        <h3>Backup Strategy</h3>
        
        <div class="step">
            <h4>Automated Backup Script</h4>
            <div class="code-block">cat << 'EOF' > backup.sh
#!/bin/bash
BACKUP_DIR="backups/$(date +%Y%m%d)"
mkdir -p $BACKUP_DIR

# Backup database
cp data/pulsechain_news.db $BACKUP_DIR/

# Backup configuration
cp .env $BACKUP_DIR/

# Create archive
tar -czf $BACKUP_DIR.tar.gz $BACKUP_DIR/
rm -rf $BACKUP_DIR/

echo "Backup completed: $BACKUP_DIR.tar.gz"
EOF

chmod +x backup.sh
./backup.sh</div>
        </div>

        <h3>Health Check Script</h3>
        
        <div class="step">
            <h4>Automated Health Monitoring</h4>
            <div class="code-block">cat << 'EOF' > health_check.sh
#!/bin/bash

echo "🏥 PulseChain News Hub Health Check"
echo "=================================="

# Check if server is running
if curl -s http://localhost:3001/api/health > /dev/null; then
    echo "✅ Server: Running"
    
    # Get detailed health
    HEALTH=$(curl -s http://localhost:3001/api/health)
    DB_STATUS=$(echo $HEALTH | jq -r '.services.database.success')
    AI_STATUS=$(echo $HEALTH | jq -r '.services.deepseek.success')
    
    echo "✅ Database: $DB_STATUS"
    echo "✅ DeepSeek AI: $AI_STATUS"
    echo "⏱️  Uptime: $(echo $HEALTH | jq -r '.uptime') seconds"
else
    echo "❌ Server: Not running"
    echo "🔄 Attempting restart..."
    nohup npm start > server.log 2>&1 &
fi

echo "=================================="
EOF

chmod +x health_check.sh

# Add to crontab for monitoring every 5 minutes:
# crontab -e
# */5 * * * * /path/to/health_check.sh >> health.log</div>
        </div>
    </section>

    <section class="section">
        <h2>📞 Support & Resources</h2>
        
        <h3>Quick Reference Commands</h3>
        <div class="code-block"># Start server
npm start

# Stop server
Ctrl+C (or kill process)

# Check status
curl http://localhost:3001/api/health

# View logs
tail -f server.log

# Test connections
npm test

# Update dependencies
npm update && npm audit fix</div>

        <h3>Important File Locations</h3>
        <ul>
            <li><strong>Configuration:</strong> <span class="file-path">.env</span></li>
            <li><strong>Database:</strong> <span class="file-path">data/pulsechain_news.db</span></li>
            <li><strong>Logs:</strong> <span class="file-path">server.log</span></li>
            <li><strong>Main Server:</strong> <span class="file-path">server.js</span></li>
            <li><strong>Services:</strong> <span class="file-path">src/services/</span></li>
        </ul>

        <div class="success">
            <h4>🎉 Congratulations!</h4>
            <p>Your PulseChain News Hub is now fully configured and ready to use. 
            The system will automatically scrape news, analyze sentiment with AI, and provide trading insights.</p>
            
            <p><strong>Access your dashboard:</strong> <a href="http://localhost:3001/dashboard" target="_blank">http://localhost:3001/dashboard</a></p>
        </div>

        <div class="info">
            <strong>💡 Pro Tips:</strong>
            <ul>
                <li>Monitor DeepSeek API usage to control costs</li>
                <li>Regularly backup your database</li>
                <li>Keep API keys secure and rotate them periodically</li>
                <li>Check logs regularly for any issues</li>
                <li>Update dependencies monthly for security patches</li>
            </ul>
        </div>
    </section>

    <footer style="text-align: center; padding: 30px; color: #666; border-top: 1px solid #eee; margin-top: 40px;">
        <p><strong>PulseChain News Hub</strong> - AI-Powered Crypto News Analysis Platform</p>
        <p>Built with ❤️ for the PulseChain community</p>
        <p><em>Last updated: September 2025</em></p>
    </footer>
</body>
</html>