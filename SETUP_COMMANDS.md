# 🚀 PulseChain News Hub - Complete Setup Commands

## Table of Contents
- [System Version Checks](#system-version-checks)
- [Prerequisites Installation](#prerequisites-installation)
- [Node.js Setup](#nodejs-setup)
- [Project Dependencies](#project-dependencies)
- [Database Setup](#database-setup)
- [Configuration](#configuration)
- [Server Management](#server-management)
- [Health Checks](#health-checks)
- [Troubleshooting Commands](#troubleshooting-commands)
- [Maintenance Scripts](#maintenance-scripts)

---

## System Version Checks

### Check Current System
```bash
# Check OS version
lsb_release -a
cat /etc/os-release
uname -a

# Check architecture
uname -m

# Check available memory
free -h

# Check disk space
df -h

# Check current user
whoami
```

### Check Existing Dependencies
```bash
# Check if curl is installed
curl --version || echo "curl not installed"

# Check if wget is installed
wget --version || echo "wget not installed"

# Check if git is installed
git --version || echo "git not installed"

# Check if build tools are available
gcc --version || echo "gcc not installed"
make --version || echo "make not installed"

# Check if python3 is installed
python3 --version || echo "python3 not installed"

# Check if Node.js is installed
node --version || echo "node not installed"
npm --version || echo "npm not installed"
```

### Network Connectivity Test
```bash
# Test internet connection
ping -c 4 google.com

# Test DNS resolution
nslookup github.com

# Test HTTPS connectivity
curl -I https://api.deepseek.com/
curl -I https://dexscreener.com/
```

---

## Prerequisites Installation

### Update System Packages
```bash
# Update package lists
sudo apt update

# Upgrade existing packages (optional)
sudo apt upgrade -y

# Install essential packages
sudo apt install -y curl wget git unzip
```

### Install Build Tools
```bash
# Install build essentials
sudo apt install -y build-essential

# Install Python development tools
sudo apt install -y python3-dev python3-distutils python3-pip

# Verify installation
gcc --version
python3 --version
```

### Install Additional Utilities
```bash
# Install JSON processor (for API testing)
sudo apt install -y jq

# Install process monitoring tools
sudo apt install -y htop

# Install network tools
sudo apt install -y net-tools lsof

# Verify installations
jq --version
htop --version
lsof -v
```

---

## Node.js Setup

### Method 1: NodeSource Repository (Recommended)
```bash
# Add NodeSource repository
curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -

# Install Node.js
sudo apt install -y nodejs

# Verify installation
node --version
npm --version

# Should output something like:
# v20.x.x
# 10.x.x
```

### Method 2: Using NVM (Alternative)
```bash
# Install NVM
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash

# Reload shell
source ~/.bashrc

# Install latest LTS Node.js
nvm install --lts
nvm use --lts

# Verify
node --version
npm --version
```

### Fix NPM Permissions (if needed)
```bash
# Create npm global directory
mkdir ~/.npm-global

# Configure npm to use new directory
npm config set prefix '~/.npm-global'

# Add to PATH
echo 'export PATH=~/.npm-global/bin:$PATH' >> ~/.bashrc
source ~/.bashrc

# Verify
npm config get prefix
```

---

## Project Dependencies

### Clone/Download Project
```bash
# Option A: If you have project files
cd /path/to/your/pulsechain_news_hub

# Option B: Create new directory
mkdir -p ~/pulsechain_news_hub
cd ~/pulsechain_news_hub

# Option C: If cloning from git
git clone [repository-url] pulsechain_news_hub
cd pulsechain_news_hub
```

### Install Node.js Dependencies
```bash
# Install all dependencies
npm install

# Alternative: Install with verbose output
npm install --verbose

# Alternative: Install without optional dependencies
npm install --no-optional

# Verify package.json exists
cat package.json | jq '.dependencies'
```

### Check Installed Packages
```bash
# List installed packages
npm list --depth=0

# Check for outdated packages
npm outdated

# Check for security vulnerabilities
npm audit

# Fix vulnerabilities (if any)
npm audit fix
```

---

## Database Setup

### Create Database Directory
```bash
# Create data directory
mkdir -p data

# Set proper permissions
chmod 755 data

# Verify directory exists
ls -la data/
```

### Database Schema (SQLite)
```sql
-- PulseChain News Hub Database Schema
-- SQLite database for local development

-- News Sources Table
CREATE TABLE IF NOT EXISTS sources (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL UNIQUE,
    url TEXT NOT NULL,
    type TEXT NOT NULL, -- 'rss', 'web', 'social'
    credibility_score REAL DEFAULT 5.0,
    is_active BOOLEAN DEFAULT 1,
    tags TEXT, -- JSON array of tags
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    last_scraped_at DATETIME,
    total_articles INTEGER DEFAULT 0,
    success_rate REAL DEFAULT 0.0
);

-- Articles Table
CREATE TABLE IF NOT EXISTS articles (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    source_id INTEGER NOT NULL,
    title TEXT NOT NULL,
    content TEXT,
    description TEXT,
    url TEXT NOT NULL UNIQUE,
    published_at DATETIME NOT NULL,
    scraped_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    author TEXT,
    image_url TEXT,
    guid TEXT,
    categories TEXT, -- JSON array
    
    -- AI Analysis Results
    sentiment_score REAL,
    sentiment_label TEXT, -- 'positive', 'negative', 'neutral'
    sentiment_confidence REAL,
    credibility_score REAL,
    
    -- Trading Analysis
    trading_signal TEXT, -- 'BUY', 'SELL', 'HOLD'
    signal_confidence REAL,
    signal_timeframe TEXT, -- 'short', 'medium', 'long'
    price_impact_short TEXT, -- 'positive', 'negative', 'neutral'
    price_impact_long TEXT,
    
    -- Content Enhancement
    enhanced_content TEXT,
    key_insights TEXT, -- JSON array
    manipulation_flags TEXT, -- JSON object
    
    -- Processing Status
    is_processed BOOLEAN DEFAULT 0,
    processing_error TEXT,
    processed_at DATETIME,
    
    FOREIGN KEY (source_id) REFERENCES sources (id)
);

-- DEX Data Table
CREATE TABLE IF NOT EXISTS dex_data (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    token_symbol TEXT NOT NULL,
    token_name TEXT,
    token_address TEXT,
    price_usd REAL NOT NULL,
    price_change_24h REAL,
    volume_24h REAL,
    market_cap REAL,
    liquidity REAL,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    -- Market Health Metrics
    health_score REAL,
    health_grade TEXT,
    volatility REAL,
    trading_pairs_count INTEGER,
    
    -- Data Source
    data_source TEXT DEFAULT 'multiple',
    is_verified BOOLEAN DEFAULT 0
);

-- Trading Signals Table
CREATE TABLE IF NOT EXISTS trading_signals (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    article_id INTEGER,
    token_symbol TEXT NOT NULL,
    signal_type TEXT NOT NULL, -- 'BUY', 'SELL', 'HOLD'
    confidence REAL NOT NULL,
    timeframe TEXT NOT NULL,
    
    -- Signal Details
    reasoning TEXT,
    entry_price REAL,
    target_price REAL,
    stop_loss REAL,
    risk_level TEXT, -- 'low', 'medium', 'high'
    
    -- Market Context
    market_sentiment TEXT,
    volume_analysis TEXT,
    news_catalyst TEXT,
    
    -- Performance Tracking
    is_active BOOLEAN DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    expires_at DATETIME,
    actual_outcome TEXT, -- 'success', 'failure', 'partial'
    performance_score REAL,
    
    FOREIGN KEY (article_id) REFERENCES articles (id)
);

-- Price History Table
CREATE TABLE IF NOT EXISTS price_history (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    token_symbol TEXT NOT NULL,
    price_usd REAL NOT NULL,
    volume_24h REAL,
    market_cap REAL,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    -- OHLC Data
    open_price REAL,
    high_price REAL,
    low_price REAL,
    close_price REAL,
    
    -- Technical Indicators
    rsi REAL,
    moving_avg_20 REAL,
    moving_avg_50 REAL
);

-- News-Price Correlation Table
CREATE TABLE IF NOT EXISTS news_correlations (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    article_id INTEGER NOT NULL,
    token_symbol TEXT NOT NULL,
    
    -- Correlation Metrics
    correlation_strength REAL, -- -1.0 to 1.0
    time_lag_minutes INTEGER, -- How long after news did price react
    price_impact_percent REAL,
    volume_impact_percent REAL,
    
    -- Analysis Window
    analysis_window_hours INTEGER DEFAULT 24,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (article_id) REFERENCES articles (id)
);

-- Analytics Table
CREATE TABLE IF NOT EXISTS analytics (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    metric_name TEXT NOT NULL,
    metric_value REAL NOT NULL,
    metric_type TEXT NOT NULL, -- 'count', 'rate', 'score', 'percentage'
    category TEXT, -- 'scraping', 'analysis', 'trading', 'performance'
    
    -- Time-based metrics
    time_period TEXT, -- 'hourly', 'daily', 'weekly'
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    -- Additional context
    metadata TEXT -- JSON object for extra data
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_articles_published_at ON articles (published_at);
CREATE INDEX IF NOT EXISTS idx_articles_source_id ON articles (source_id);
CREATE INDEX IF NOT EXISTS idx_articles_sentiment ON articles (sentiment_label, sentiment_score);
CREATE INDEX IF NOT EXISTS idx_articles_processed ON articles (is_processed, processed_at);

CREATE INDEX IF NOT EXISTS idx_dex_data_symbol_timestamp ON dex_data (token_symbol, timestamp);
CREATE INDEX IF NOT EXISTS idx_trading_signals_active ON trading_signals (is_active, created_at);
CREATE INDEX IF NOT EXISTS idx_trading_signals_token ON trading_signals (token_symbol, signal_type);
CREATE INDEX IF NOT EXISTS idx_price_history_token_timestamp ON price_history (token_symbol, timestamp);
CREATE INDEX IF NOT EXISTS idx_analytics_metric_timestamp ON analytics (metric_name, timestamp);

-- Insert default sources
INSERT OR IGNORE INTO sources (name, url, type, credibility_score, tags) VALUES
('CoinTelegraph', 'https://cointelegraph.com/rss', 'rss', 8.5, '["general", "analysis"]'),
('CryptoNews', 'https://cryptonews.com/news/feed/', 'rss', 7.8, '["general", "breaking"]'),
('Decrypt', 'https://decrypt.co/feed', 'rss', 8.2, '["defi", "technology"]'),
('CoinDesk', 'https://www.coindesk.com/arc/outboundfeeds/rss/', 'rss', 9.0, '["institutional", "analysis"]'),
('BeInCrypto', 'https://beincrypto.com/feed/', 'rss', 7.5, '["altcoins", "defi"]'),
('CryptoSlate', 'https://cryptoslate.com/feed/', 'rss', 8.0, '["analysis", "projects"]'),
('AMBCrypto', 'https://ambcrypto.com/feed/', 'rss', 7.2, '["analysis", "market"]'),
('NewsBTC', 'https://www.newsbtc.com/feed/', 'rss', 7.8, '["technical", "trading"]'),
('Reddit PulseChain', 'https://www.reddit.com/r/PulseChain/.rss', 'social', 6.5, '["community", "social"]');
```

### Initialize Database Manually (if needed)
```bash
# Install SQLite3 command line tool
sudo apt install -y sqlite3

# Create database and tables
sqlite3 data/pulsechain_news.db < src/database/schema.sql

# Verify tables were created
sqlite3 data/pulsechain_news.db ".tables"

# Check sources table
sqlite3 data/pulsechain_news.db "SELECT * FROM sources LIMIT 5;"
```

---

## Configuration

### Create Environment File
```bash
# Create .env file from template
cat << 'EOF' > .env
# DeepSeek API Configuration
DEEPSEEK_API_KEY=sk-your-deepseek-api-key-here
DEEPSEEK_BASE_URL=https://api.deepseek.com

# Server Configuration
PORT=3001
NODE_ENV=development

# WebSocket Configuration
WS_PORT=3002

# Database Configuration
DB_PATH=./data/pulsechain_news.db

# News Scraping Configuration
SCRAPING_INTERVAL=300000
MAX_ARTICLES_PER_SOURCE=50

# DEX Data Configuration
PULSECHAIN_RPC=https://rpc.pulsechain.com
PULSEX_API=https://api.pulsex.com/api/v1
DEX_REFRESH_INTERVAL=60000

# Rate Limiting
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=100
EOF

# Set proper permissions
chmod 600 .env

# Edit with your actual API keys
nano .env
```

### Verify Configuration
```bash
# Check .env file exists
ls -la .env

# Display configuration (without sensitive data)
grep -v "API_KEY\|PASSWORD" .env

# Test environment loading
node -e "require('dotenv').config(); console.log('Port:', process.env.PORT);"
```

---

## Server Management

### Start Server Commands
```bash
# Development mode (with auto-restart)
npm run dev

# Production mode
npm start

# Start in background
nohup npm start > server.log 2>&1 &

# Start with PM2 (if installed)
npm install -g pm2
pm2 start server.js --name "pulsechain-news"
```

### Stop Server Commands
```bash
# Stop with Ctrl+C (if running in foreground)
# Press Ctrl+C

# Stop background process
ps aux | grep node
kill [PID]

# Force kill all node processes
pkill -f "node server.js"

# Stop PM2 process
pm2 stop pulsechain-news
pm2 delete pulsechain-news
```

### Monitor Server
```bash
# View real-time logs
tail -f server.log

# Monitor system resources
htop

# Check port usage
sudo lsof -i :3001
sudo netstat -tulpn | grep :3001

# Monitor with PM2
pm2 status
pm2 logs pulsechain-news
```

---

## Health Checks

### API Health Checks
```bash
# Basic health check
curl http://localhost:3001/api/health

# Pretty print JSON response
curl -s http://localhost:3001/api/health | jq '.'

# Check specific services
curl -s http://localhost:3001/api/health | jq '.services'

# Test database connection
curl -s http://localhost:3001/api/health | jq '.services.database'

# Test DeepSeek connection
curl -s http://localhost:3001/api/health | jq '.services.deepseek'
```

### API Endpoint Tests
```bash
# Test articles endpoint
curl -s http://localhost:3001/api/articles | jq '.success'

# Test DEX data endpoint
curl -s http://localhost:3001/api/dex/current | jq '.data.prices'

# Test trading signals
curl -s http://localhost:3001/api/signals | jq '.data | length'

# Test dashboard data
curl -s http://localhost:3001/api/dashboard | jq '.success'

# Test news sources
curl -s http://localhost:3001/api/sources | jq '.data | length'
```

### Connection Tests
```bash
# Run built-in connection tests
npm test

# Test individual components
node -e "require('./src/services/deepseek').testConnection().then(console.log)"

# Test database
node -e "require('./src/database/database').testConnection().then(console.log)"

# Test DEX services
node -e "require('./src/services/dex').getServiceStats()"
```

---

## Troubleshooting Commands

### Port Issues
```bash
# Find process using port 3001
sudo lsof -i :3001

# Kill process on specific port
sudo kill -9 $(sudo lsof -t -i:3001)

# Use alternative port
PORT=3005 npm start
```

### Permission Issues
```bash
# Fix project permissions
sudo chown -R $USER:$USER .
chmod -R 755 .

# Fix npm cache permissions
npm cache clean --force
sudo chown -R $USER:$USER ~/.npm
```

### Database Issues
```bash
# Remove corrupted database
rm -f data/pulsechain_news.db*

# Recreate database
mkdir -p data

# Check database file permissions
ls -la data/

# Test database creation
sqlite3 data/test.db "SELECT datetime('now');"
rm data/test.db
```

### Network Issues
```bash
# Test internet connectivity
ping -c 4 *******

# Test DNS resolution
nslookup api.deepseek.com

# Test API endpoints
curl -I https://api.deepseek.com/
curl -I https://api.dexscreener.com/

# Check firewall status
sudo ufw status

# Test local connectivity
curl -I http://localhost:3001/
```

### Memory Issues
```bash
# Check memory usage
free -h

# Check Node.js memory usage
ps aux | grep node

# Increase Node.js memory limit
node --max-old-space-size=4096 server.js

# Monitor memory in real-time
watch -n 2 'free -h'
```

---

## Maintenance Scripts

### Update Dependencies
```bash
# Check for outdated packages
npm outdated

# Update all packages
npm update

# Update specific package
npm install express@latest

# Check for security issues
npm audit

# Fix security vulnerabilities
npm audit fix --force
```

### Database Maintenance
```bash
# Backup database
cp data/pulsechain_news.db "data/backup_$(date +%Y%m%d_%H%M%S).db"

# Check database size
du -h data/pulsechain_news.db

# Vacuum database (optimize)
sqlite3 data/pulsechain_news.db "VACUUM;"

# Check database integrity
sqlite3 data/pulsechain_news.db "PRAGMA integrity_check;"
```

### Log Management
```bash
# Rotate logs
mv server.log "server_$(date +%Y%m%d_%H%M%S).log"
touch server.log

# Clean old logs (older than 7 days)
find . -name "server_*.log" -mtime +7 -delete

# Monitor log size
du -h server.log

# Filter logs by level
grep "ERROR" server.log
grep "SUCCESS" server.log
```

### System Cleanup
```bash
# Clean npm cache
npm cache clean --force

# Remove node_modules and reinstall
rm -rf node_modules
npm install

# Clean temporary files
find . -name ".DS_Store" -delete
find . -name "*.tmp" -delete

# Check disk usage
df -h
du -sh *
```

### Automated Health Check Script
```bash
# Create health check script
cat << 'EOF' > health_check.sh
#!/bin/bash
echo "=== PulseChain News Hub Health Check ==="
echo "Timestamp: $(date)"

# Check server status
if curl -s http://localhost:3001/api/health > /dev/null; then
    echo "✅ Server: Running"
    
    # Get health details
    HEALTH=$(curl -s http://localhost:3001/api/health)
    echo "📊 Uptime: $(echo $HEALTH | jq -r '.uptime') seconds"
    echo "💾 Memory: $(echo $HEALTH | jq -r '.memory.heapUsed')"
    echo "🔗 Database: $(echo $HEALTH | jq -r '.services.database.success')"
    echo "🤖 DeepSeek: $(echo $HEALTH | jq -r '.services.deepseek.success')"
else
    echo "❌ Server: Not responding"
    echo "🔄 Check if process is running:"
    ps aux | grep "node server.js" | grep -v grep
fi

echo "========================================="
EOF

chmod +x health_check.sh
./health_check.sh
```

### Complete System Status Check
```bash
# Create comprehensive status script
cat << 'EOF' > system_status.sh
#!/bin/bash
echo "🏥 COMPLETE SYSTEM STATUS CHECK"
echo "=============================="

echo "📋 SYSTEM INFO:"
echo "OS: $(lsb_release -d | cut -f2)"
echo "Kernel: $(uname -r)"
echo "Architecture: $(uname -m)"
echo "Uptime: $(uptime -p)"

echo ""
echo "💻 RESOURCES:"
echo "Memory: $(free -h | grep '^Mem:' | awk '{print $3 "/" $2}')"
echo "Disk: $(df -h / | tail -1 | awk '{print $3 "/" $2 " (" $5 " used)"}')"
echo "Load: $(uptime | awk -F'load average:' '{print $2}')"

echo ""
echo "🔧 DEPENDENCIES:"
echo "Node.js: $(node --version 2>/dev/null || echo 'Not installed')"
echo "NPM: $(npm --version 2>/dev/null || echo 'Not installed')"
echo "Git: $(git --version 2>/dev/null | awk '{print $3}' || echo 'Not installed')"
echo "SQLite: $(sqlite3 --version 2>/dev/null | awk '{print $1}' || echo 'Not installed')"

echo ""
echo "📦 PROJECT STATUS:"
if [ -f package.json ]; then
    echo "✅ package.json found"
    echo "📊 Dependencies: $(jq -r '.dependencies | keys | length' package.json)"
else
    echo "❌ package.json not found"
fi

if [ -f .env ]; then
    echo "✅ .env configuration found"
else
    echo "❌ .env configuration missing"
fi

if [ -d node_modules ]; then
    echo "✅ node_modules installed"
else
    echo "❌ node_modules missing - run 'npm install'"
fi

echo ""
echo "🌐 NETWORK:"
ping -c 1 google.com &>/dev/null && echo "✅ Internet: Connected" || echo "❌ Internet: Disconnected"

echo ""
echo "🚀 SERVER STATUS:"
if pgrep -f "node server.js" > /dev/null; then
    echo "✅ Server: Running (PID: $(pgrep -f 'node server.js'))"
    
    if curl -s http://localhost:3001/api/health > /dev/null; then
        echo "✅ API: Responding"
        HEALTH=$(curl -s http://localhost:3001/api/health)
        echo "   Uptime: $(echo $HEALTH | jq -r '.uptime // "unknown"') seconds"
        echo "   Status: $(echo $HEALTH | jq -r '.status // "unknown"')"
    else
        echo "❌ API: Not responding"
    fi
else
    echo "❌ Server: Not running"
fi

echo "=============================="
EOF

chmod +x system_status.sh
./system_status.sh
```

---

## Quick Start Commands

### Complete Setup (Fresh Install)
```bash
# Run this complete setup script
cat << 'EOF' > quick_setup.sh
#!/bin/bash
echo "🚀 PulseChain News Hub Quick Setup"

# Update system
sudo apt update

# Install dependencies
sudo apt install -y curl wget git build-essential python3-dev python3-distutils jq

# Install Node.js
curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
sudo apt install -y nodejs

# Create project directory
mkdir -p ~/pulsechain_news_hub
cd ~/pulsechain_news_hub

# Install npm dependencies (assuming package.json exists)
npm install

# Create data directory
mkdir -p data

# Create basic .env file
echo "DEEPSEEK_API_KEY=sk-your-api-key-here" > .env
echo "PORT=3001" >> .env
echo "DB_PATH=./data/pulsechain_news.db" >> .env

echo "✅ Setup completed!"
echo "📝 Edit .env file with your API keys"
echo "🚀 Run 'npm start' to start the server"
EOF

chmod +x quick_setup.sh
./quick_setup.sh
```

### Daily Operations
```bash
# Morning startup routine
./system_status.sh
npm start

# Midday health check
curl -s http://localhost:3001/api/health | jq '.services'

# Evening maintenance
npm audit
./health_check.sh
```

---

## Version Requirements Summary

| Component | Minimum Version | Recommended | Check Command |
|-----------|----------------|-------------|---------------|
| Ubuntu/Linux Mint | 18.04/19+ | 20.04/20+ | `lsb_release -a` |
| Node.js | 18.0.0 | 20.x.x | `node --version` |
| NPM | 8.0.0 | 10.x.x | `npm --version` |
| Git | 2.0+ | Latest | `git --version` |
| Python3 | 3.6+ | 3.8+ | `python3 --version` |
| SQLite3 | 3.7+ | Latest | `sqlite3 --version` |

---

This markdown file contains all the bash commands, SQL schemas, and version checks you need to set up and maintain your PulseChain News Hub! 🚀