<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PulseChain News Hub - Step by Step Setup Guide</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f8fafc;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px;
            border-radius: 15px;
            text-align: center;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .header h1 {
            font-size: 2.8em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .progress-bar {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }

        .progress-steps {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 10px;
        }

        .step-indicator {
            flex: 1;
            min-width: 120px;
            text-align: center;
            padding: 10px;
            background: #e2e8f0;
            color: #64748b;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s;
        }

        .step-indicator.active {
            background: #667eea;
            color: white;
        }

        .step-indicator.completed {
            background: #10b981;
            color: white;
        }

        .step {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 20px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            border-left: 5px solid #667eea;
        }

        .step h2 {
            color: #667eea;
            font-size: 1.8em;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .step-number {
            background: #667eea;
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 1.2em;
        }

        .substep {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
            border-left: 4px solid #10b981;
        }

        .substep h3 {
            color: #1e293b;
            margin-bottom: 10px;
            font-size: 1.2em;
        }

        .command-box {
            background: #1e293b;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 10px;
            font-family: 'Monaco', 'Consolas', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 15px 0;
            position: relative;
        }

        .command-box .copy-btn {
            position: absolute;
            top: 10px;
            right: 10px;
            background: #667eea;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 12px;
        }

        .command-box .copy-btn:hover {
            background: #5a6fd8;
        }

        .expected-output {
            background: #dcfce7;
            border: 1px solid #16a34a;
            color: #166534;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Monaco', 'Consolas', monospace;
            font-size: 13px;
            margin: 10px 0;
        }

        .expected-output::before {
            content: "✅ Expected Output:";
            font-weight: bold;
            display: block;
            margin-bottom: 10px;
            font-family: 'Segoe UI', sans-serif;
        }

        .warning {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            color: #92400e;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }

        .warning::before {
            content: "⚠️ Important:";
            font-weight: bold;
            display: block;
            margin-bottom: 5px;
        }

        .success {
            background: #dcfce7;
            border: 1px solid #16a34a;
            color: #166534;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }

        .success::before {
            content: "✅ Success:";
            font-weight: bold;
            display: block;
            margin-bottom: 5px;
        }

        .info {
            background: #dbeafe;
            border: 1px solid #3b82f6;
            color: #1e40af;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }

        .info::before {
            content: "💡 Info:";
            font-weight: bold;
            display: block;
            margin-bottom: 5px;
        }

        .directory-indicator {
            background: #ede9fe;
            border: 2px solid #8b5cf6;
            color: #5b21b6;
            padding: 10px 15px;
            border-radius: 8px;
            font-family: 'Monaco', 'Consolas', monospace;
            font-weight: bold;
            margin: 10px 0;
            display: inline-block;
        }

        .directory-indicator::before {
            content: "📁 Current Directory: ";
            font-family: 'Segoe UI', sans-serif;
            font-weight: normal;
        }

        .checklist {
            background: #f0f9ff;
            border: 1px solid #0ea5e9;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
        }

        .checklist h4 {
            color: #0c4a6e;
            margin-bottom: 15px;
            font-size: 1.1em;
        }

        .checklist ul {
            list-style: none;
        }

        .checklist li {
            padding: 5px 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .checklist li::before {
            content: "☐";
            font-size: 1.2em;
            color: #0ea5e9;
        }

        .navigation {
            position: sticky;
            top: 20px;
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }

        .navigation h3 {
            color: #667eea;
            margin-bottom: 15px;
        }

        .navigation ul {
            list-style: none;
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }

        .navigation a {
            color: #667eea;
            text-decoration: none;
            padding: 8px 15px;
            background: #f1f5f9;
            border-radius: 20px;
            font-size: 14px;
            transition: all 0.3s;
        }

        .navigation a:hover {
            background: #667eea;
            color: white;
        }

        .final-check {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            margin: 30px 0;
        }

        .final-check h2 {
            font-size: 2em;
            margin-bottom: 15px;
        }

        .access-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .access-link {
            background: rgba(255,255,255,0.2);
            padding: 15px;
            border-radius: 10px;
            text-decoration: none;
            color: white;
            transition: all 0.3s;
        }

        .access-link:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .progress-steps {
                flex-direction: column;
            }
            
            .step-indicator {
                min-width: auto;
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 PulseChain News Hub</h1>
            <p>Complete Step-by-Step Setup Guide for Beginners</p>
            <p><strong>From Zero to AI-Powered News Analysis in 30 Minutes!</strong></p>
        </div>

        <div class="progress-bar">
            <div class="progress-steps">
                <div class="step-indicator" id="step1-indicator">1. Check System</div>
                <div class="step-indicator" id="step2-indicator">2. Install Tools</div>
                <div class="step-indicator" id="step3-indicator">3. Get Project</div>
                <div class="step-indicator" id="step4-indicator">4. Configure</div>
                <div class="step-indicator" id="step5-indicator">5. Launch!</div>
            </div>
        </div>

        <nav class="navigation">
            <h3>Quick Navigation</h3>
            <ul>
                <li><a href="#step1">Check System</a></li>
                <li><a href="#step2">Install Dependencies</a></li>
                <li><a href="#step3">Get Project Files</a></li>
                <li><a href="#step4">Configuration</a></li>
                <li><a href="#step5">Launch Server</a></li>
                <li><a href="#troubleshooting">Troubleshooting</a></li>
            </ul>
        </nav>

        <!-- STEP 1: SYSTEM CHECK -->
        <div class="step" id="step1">
            <h2><span class="step-number">1</span>Check Your System</h2>
            <p>Let's make sure your system is ready. We'll check what you have and what you need.</p>

            <div class="substep">
                <h3>Open Terminal</h3>
                <p>Press <strong>Ctrl + Alt + T</strong> to open a terminal window.</p>
                <div class="directory-indicator">~ (your home directory)</div>
            </div>

            <div class="substep">
                <h3>Check Your Operating System</h3>
                <div class="command-box">
                    <button class="copy-btn" onclick="copyToClipboard('lsb_release -a')">Copy</button>
lsb_release -a
                </div>
                <div class="expected-output">No LSB modules are available.
Distributor ID: Ubuntu
Description:    Ubuntu 20.04.6 LTS
Release:        20.04
Codename:       focal</div>
                <div class="info">This should show Ubuntu 18.04+ or Linux Mint 19+. If you see this, you're good to go!</div>
            </div>

            <div class="substep">
                <h3>Check What's Already Installed</h3>
                <p>Let's see what tools you already have:</p>
                
                <div class="command-box">
                    <button class="copy-btn" onclick="copyToClipboard('node --version || echo \"Node.js not installed\"')">Copy</button>
node --version || echo "Node.js not installed"
                </div>

                <div class="command-box">
                    <button class="copy-btn" onclick="copyToClipboard('npm --version || echo \"NPM not installed\"')">Copy</button>
npm --version || echo "NPM not installed"
                </div>

                <div class="command-box">
                    <button class="copy-btn" onclick="copyToClipboard('git --version || echo \"Git not installed\"')">Copy</button>
git --version || echo "Git not installed"
                </div>

                <div class="checklist">
                    <h4>What You Should See:</h4>
                    <ul>
                        <li>If you see version numbers (like v20.x.x) - Great! ✅</li>
                        <li>If you see "not installed" - No problem, we'll install it next! 📦</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- STEP 2: INSTALL DEPENDENCIES -->
        <div class="step" id="step2">
            <h2><span class="step-number">2</span>Install Required Tools</h2>
            <p>Now we'll install everything you need. Don't worry, this is automated!</p>

            <div class="substep">
                <h3>Update Your System</h3>
                <p>First, let's make sure your system is up to date:</p>
                <div class="directory-indicator">~ (home directory)</div>
                
                <div class="command-box">
                    <button class="copy-btn" onclick="copyToClipboard('sudo apt update')">Copy</button>
sudo apt update
                </div>
                <div class="warning">You'll be asked for your password. Type it and press Enter. The text won't show as you type - this is normal!</div>
            </div>

            <div class="substep">
                <h3>Install Basic Tools</h3>
                <div class="command-box">
                    <button class="copy-btn" onclick="copyToClipboard('sudo apt install -y curl wget git build-essential python3-dev python3-distutils jq')">Copy</button>
sudo apt install -y curl wget git build-essential python3-dev python3-distutils jq
                </div>
                <div class="info">This installs curl, wget, git, build tools, Python development files, and jq (JSON processor). The -y flag automatically says "yes" to all prompts.</div>
            </div>

            <div class="substep">
                <h3>Install Node.js (The Important One!)</h3>
                <p>Node.js is what runs our news hub. Let's install the latest version:</p>
                
                <div class="command-box">
                    <button class="copy-btn" onclick="copyToClipboard('curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -')">Copy</button>
curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
                </div>
                <div class="info">This downloads and sets up the Node.js repository.</div>

                <div class="command-box">
                    <button class="copy-btn" onclick="copyToClipboard('sudo apt install -y nodejs')">Copy</button>
sudo apt install -y nodejs
                </div>
            </div>

            <div class="substep">
                <h3>Verify Everything is Installed</h3>
                <div class="command-box">
                    <button class="copy-btn" onclick="copyToClipboard('node --version && npm --version && git --version')">Copy</button>
node --version && npm --version && git --version
                </div>
                <div class="expected-output">v20.11.0
10.2.4
git version 2.25.1</div>
                <div class="success">If you see version numbers like above, everything is installed correctly!</div>
            </div>
        </div>

        <!-- STEP 3: GET PROJECT FILES -->
        <div class="step" id="step3">
            <h2><span class="step-number">3</span>Get the Project Files</h2>
            <p>Now let's get the PulseChain News Hub files on your computer.</p>

            <div class="substep">
                <h3>Create Project Directory</h3>
                <p>Let's create a nice place for our project:</p>
                <div class="directory-indicator">~ (home directory)</div>

                <div class="command-box">
                    <button class="copy-btn" onclick="copyToClipboard('mkdir -p ~/PulseChain-News-Hub')">Copy</button>
mkdir -p ~/PulseChain-News-Hub
                </div>

                <div class="command-box">
                    <button class="copy-btn" onclick="copyToClipboard('cd ~/PulseChain-News-Hub')">Copy</button>
cd ~/PulseChain-News-Hub
                </div>
                <div class="directory-indicator">~/PulseChain-News-Hub</div>
                <div class="info">The ~ symbol means your home directory. Now you're in your project folder!</div>
            </div>

            <div class="substep">
                <h3>Option A: If You Have Project Files</h3>
                <p>If you already have the project files (like from a USB or download):</p>
                
                <div class="command-box">
                    <button class="copy-btn" onclick="copyToClipboard('cp -r /path/to/your/pulsechain_news_hub/* .')">Copy</button>
# Replace /path/to/your/pulsechain_news_hub with actual path
cp -r /path/to/your/pulsechain_news_hub/* .
                </div>
                <div class="info">Replace "/path/to/your/pulsechain_news_hub" with where you have the files. For example, if they're in Downloads: cp -r ~/Downloads/pulsechain_news_hub/* .</div>
            </div>

            <div class="substep">
                <h3>Option B: Create Files Manually</h3>
                <p>If you're starting fresh, let's create the basic structure:</p>

                <div class="command-box">
                    <button class="copy-btn" onclick="copyToClipboard('mkdir -p src/database src/services data pages js css')">Copy</button>
mkdir -p src/database src/services data pages js css
                </div>

                <div class="command-box">
                    <button class="copy-btn" onclick="copyToClipboard('ls -la')">Copy</button>
ls -la
                </div>
                <div class="expected-output">drwxr-xr-x  8 <USER> <GROUP> 4096 Sep  7 10:30 .
drwxr-xr-x 25 <USER> <GROUP> 4096 Sep  7 10:29 ..
drwxr-xr-x  2 <USER> <GROUP> 4096 Sep  7 10:30 css
drwxr-xr-x  2 <USER> <GROUP> 4096 Sep  7 10:30 data
drwxr-xr-x  2 <USER> <GROUP> 4096 Sep  7 10:30 js
drwxr-xr-x  2 <USER> <GROUP> 4096 Sep  7 10:30 pages
drwxr-xr-x  4 <USER> <GROUP> 4096 Sep  7 10:30 src</div>
            </div>

            <div class="substep">
                <h3>Create package.json (The Project Blueprint)</h3>
                <div class="command-box">
                    <button class="copy-btn" onclick="copyToClipboard(packageJsonContent)">Copy</button>
cat << 'EOF' > package.json
{
  "name": "pulsechain-news-hub",
  "version": "1.0.0",
  "description": "AI-powered PulseChain news aggregator with trading signals",
  "main": "server.js",
  "scripts": {
    "start": "node server.js",
    "dev": "nodemon server.js",
    "test": "node test-connections.js"
  },
  "dependencies": {
    "express": "^4.18.2",
    "cors": "^2.8.5",
    "dotenv": "^16.3.1",
    "axios": "^1.6.0",
    "cheerio": "^1.0.0-rc.12",
    "node-cron": "^3.0.3",
    "better-sqlite3": "^9.2.2",
    "ws": "^8.14.2",
    "rss-parser": "^3.13.0",
    "express-rate-limit": "^7.1.5"
  },
  "devDependencies": {
    "nodemon": "^3.0.1"
  },
  "engines": {
    "node": ">=18.0.0"
  },
  "author": "PulseChain News Hub",
  "license": "MIT"
}
EOF
                </div>
                <div class="info">This creates the package.json file that tells npm what packages to install.</div>
            </div>

            <div class="substep">
                <h3>Install Project Dependencies</h3>
                <p>Now let's install all the Node.js packages our project needs:</p>
                
                <div class="command-box">
                    <button class="copy-btn" onclick="copyToClipboard('npm install')">Copy</button>
npm install
                </div>
                <div class="info">This might take a few minutes. You'll see lots of text scrolling - that's normal! NPM is downloading and installing packages.</div>
                
                <div class="success">If you see "added X packages" at the end without errors, you're good!</div>
                <div class="warning">If you see errors about "permission denied" or "EACCES", don't worry - the main packages still install.</div>
            </div>
        </div>

        <!-- STEP 4: CONFIGURATION -->
        <div class="step" id="step4">
            <h2><span class="step-number">4</span>Configure Your News Hub</h2>
            <p>Now we need to set up your configuration files. This is where the magic happens!</p>

            <div class="directory-indicator">~/PulseChain-News-Hub</div>

            <div class="substep">
                <h3>Create Environment Configuration (.env file)</h3>
                <p>This file contains all your settings and API keys:</p>

                <div class="command-box">
                    <button class="copy-btn" onclick="copyToClipboard(envContent)">Copy</button>
cat << 'EOF' > .env
# DeepSeek API Configuration
DEEPSEEK_API_KEY=sk-your-deepseek-api-key-here
DEEPSEEK_BASE_URL=https://api.deepseek.com

# Server Configuration
PORT=3001
NODE_ENV=development

# WebSocket Configuration
WS_PORT=3002

# Database Configuration
DB_PATH=./data/pulsechain_news.db

# News Scraping Configuration
SCRAPING_INTERVAL=300000
MAX_ARTICLES_PER_SOURCE=50

# DEX Data Configuration
PULSECHAIN_RPC=https://rpc.pulsechain.com
PULSEX_API=https://api.pulsex.com/api/v1
DEX_REFRESH_INTERVAL=60000

# Rate Limiting
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=100
EOF
                </div>
            </div>

            <div class="substep">
                <h3>Add Your DeepSeek API Key</h3>
                <p>You need to replace "sk-your-deepseek-api-key-here" with your actual API key.</p>

                <div class="info">
                    <strong>To get a DeepSeek API Key:</strong><br>
                    1. Visit <a href="https://platform.deepseek.com" target="_blank">https://platform.deepseek.com</a><br>
                    2. Sign up for an account<br>
                    3. Go to API Keys section<br>
                    4. Create a new API key<br>
                    5. Copy the key (starts with "sk-")
                </div>

                <div class="command-box">
                    <button class="copy-btn" onclick="copyToClipboard('nano .env')">Copy</button>
nano .env
                </div>
                <div class="info">
                    This opens the file editor. Find the line with "DEEPSEEK_API_KEY=" and replace "sk-your-deepseek-api-key-here" with your actual key.<br>
                    <strong>To save:</strong> Press Ctrl+X, then Y, then Enter.
                </div>
            </div>

            <div class="substep">
                <h3>Verify Your Configuration</h3>
                <div class="command-box">
                    <button class="copy-btn" onclick="copyToClipboard('cat .env | grep -v \"API_KEY\"')">Copy</button>
cat .env | grep -v "API_KEY"
                </div>
                <div class="info">This shows your config file without revealing your API key in the terminal.</div>
            </div>
        </div>

        <!-- STEP 5: LAUNCH -->
        <div class="step" id="step5">
            <h2><span class="step-number">5</span>Launch Your News Hub!</h2>
            <p>Time to start your AI-powered news hub! 🚀</p>

            <div class="directory-indicator">~/PulseChain-News-Hub</div>

            <div class="substep">
                <h3>Create a Basic Server File</h3>
                <p>Let's create a simple server to test everything:</p>

                <div class="command-box">
                    <button class="copy-btn" onclick="copyToClipboard(serverContent)">Copy</button>
cat << 'EOF' > server.js
const express = require('express');
const cors = require('cors');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3001;

app.use(cors());
app.use(express.json());

// Basic health check
app.get('/api/health', (req, res) => {
    res.json({
        status: 'healthy',
        message: 'PulseChain News Hub is running!',
        timestamp: new Date().toISOString(),
        version: '1.0.0'
    });
});

// Serve static files
app.use(express.static('.'));

// Basic dashboard route
app.get('/', (req, res) => {
    res.send(`
        <html>
            <body style="font-family: Arial; padding: 40px; background: #f0f4f8;">
                <div style="max-width: 800px; margin: 0 auto; background: white; padding: 40px; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
                    <h1 style="color: #667eea; text-align: center;">🚀 PulseChain News Hub</h1>
                    <h2 style="color: #10b981; text-align: center;">Successfully Running!</h2>
                    <p style="font-size: 18px; text-align: center; color: #666;">
                        Your AI-powered news aggregator is up and running.<br>
                        DeepSeek API Key: ${process.env.DEEPSEEK_API_KEY ? '✅ Configured' : '❌ Missing'}
                    </p>
                    <div style="text-align: center; margin-top: 30px;">
                        <a href="/api/health" style="background: #667eea; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; margin: 10px;">API Health Check</a>
                    </div>
                </div>
            </body>
        </html>
    `);
});

app.listen(PORT, () => {
    console.log(`🚀 PulseChain News Hub running on http://localhost:${PORT}`);
    console.log(`📊 API Health: http://localhost:${PORT}/api/health`);
    console.log(`🔑 DeepSeek API: ${process.env.DEEPSEEK_API_KEY ? 'Configured ✅' : 'Missing ❌'}`);
});
EOF
                </div>
            </div>

            <div class="substep">
                <h3>Start Your Server</h3>
                <div class="command-box">
                    <button class="copy-btn" onclick="copyToClipboard('npm start')">Copy</button>
npm start
                </div>
                <div class="expected-output">🚀 PulseChain News Hub running on http://localhost:3001
📊 API Health: http://localhost:3001/api/health
🔑 DeepSeek API: Configured ✅</div>
                <div class="success">If you see this output, congratulations! Your server is running!</div>
            </div>

            <div class="substep">
                <h3>Test Your Installation</h3>
                <p>Open a new terminal (Ctrl+Alt+T) and test your API:</p>
                <div class="directory-indicator">~ (new terminal)</div>

                <div class="command-box">
                    <button class="copy-btn" onclick="copyToClipboard('curl http://localhost:3001/api/health')">Copy</button>
curl http://localhost:3001/api/health
                </div>
                <div class="expected-output">{"status":"healthy","message":"PulseChain News Hub is running!","timestamp":"2025-09-07T10:30:00.000Z","version":"1.0.0"}</div>
            </div>
        </div>

        <!-- FINAL SUCCESS -->
        <div class="final-check">
            <h2>🎉 Congratulations! You're All Set!</h2>
            <p>Your PulseChain News Hub is now running successfully!</p>
            
            <div class="access-links">
                <a href="http://localhost:3001" class="access-link" target="_blank">
                    <strong>🏠 Main Dashboard</strong><br>
                    http://localhost:3001
                </a>
                <a href="http://localhost:3001/api/health" class="access-link" target="_blank">
                    <strong>🏥 Health Check</strong><br>
                    http://localhost:3001/api/health
                </a>
            </div>

            <div style="margin-top: 30px; padding-top: 20px; border-top: 2px solid rgba(255,255,255,0.3);">
                <h3>What's Working:</h3>
                <ul style="text-align: left; display: inline-block;">
                    <li>✅ Node.js server running</li>
                    <li>✅ Express API responding</li>
                    <li>✅ Configuration loaded</li>
                    <li>✅ DeepSeek API key configured</li>
                </ul>
            </div>
        </div>

        <!-- STOP SERVER INSTRUCTIONS -->
        <div class="step">
            <h2><span class="step-number">🛑</span>How to Stop the Server</h2>
            
            <div class="substep">
                <h3>To Stop the Server:</h3>
                <p>Go back to the terminal where the server is running and press:</p>
                <div style="text-align: center; font-size: 2em; margin: 20px 0; color: #ef4444;">
                    <strong>Ctrl + C</strong>
                </div>
                <div class="expected-output">Server shutting down...
Goodbye!</div>
            </div>

            <div class="substep">
                <h3>To Start Again Later:</h3>
                <div class="directory-indicator">~/PulseChain-News-Hub</div>
                <div class="command-box">
                    <button class="copy-btn" onclick="copyToClipboard('cd ~/PulseChain-News-Hub && npm start')">Copy</button>
cd ~/PulseChain-News-Hub && npm start
                </div>
            </div>
        </div>

        <!-- TROUBLESHOOTING -->
        <div class="step" id="troubleshooting">
            <h2><span class="step-number">🔧</span>Troubleshooting</h2>

            <div class="substep">
                <h3>Problem: "Port 3001 is already in use"</h3>
                <div class="command-box">
                    <button class="copy-btn" onclick="copyToClipboard('sudo kill -9 $(sudo lsof -t -i:3001)')">Copy</button>
sudo kill -9 $(sudo lsof -t -i:3001)
                </div>
                <div class="info">This kills any process using port 3001, then try starting again.</div>
            </div>

            <div class="substep">
                <h3>Problem: "Module not found"</h3>
                <div class="directory-indicator">~/PulseChain-News-Hub</div>
                <div class="command-box">
                    <button class="copy-btn" onclick="copyToClipboard('npm install')">Copy</button>
npm install
                </div>
                <div class="info">Reinstalls all required packages.</div>
            </div>

            <div class="substep">
                <h3>Problem: Permission Errors</h3>
                <div class="command-box">
                    <button class="copy-btn" onclick="copyToClipboard('sudo chown -R $USER:$USER ~/PulseChain-News-Hub')">Copy</button>
sudo chown -R $USER:$USER ~/PulseChain-News-Hub
                </div>
                <div class="info">Fixes file ownership issues.</div>
            </div>

            <div class="substep">
                <h3>Complete System Check</h3>
                <div class="command-box">
                    <button class="copy-btn" onclick="copyToClipboard(systemCheckContent)">Copy</button>
cat << 'EOF' > system_check.sh
#!/bin/bash
echo "🔍 PulseChain News Hub System Check"
echo "=================================="
echo "Node.js: $(node --version 2>/dev/null || echo 'Not installed ❌')"
echo "NPM: $(npm --version 2>/dev/null || echo 'Not installed ❌')"
echo "Project Dir: $(pwd)"
echo "Package.json: $([ -f package.json ] && echo 'Found ✅' || echo 'Missing ❌')"
echo ".env file: $([ -f .env ] && echo 'Found ✅' || echo 'Missing ❌')"
echo "node_modules: $([ -d node_modules ] && echo 'Installed ✅' || echo 'Missing - run npm install ❌')"
echo "Server running: $(curl -s http://localhost:3001/api/health > /dev/null && echo 'Yes ✅' || echo 'No ❌')"
echo "=================================="
EOF

chmod +x system_check.sh
./system_check.sh
                </div>
            </div>
        </div>

        <!-- NEXT STEPS -->
        <div class="step">
            <h2><span class="step-number">🚀</span>Next Steps (Advanced)</h2>
            <p>Your basic setup is complete! Here's how to add the full AI news analysis features:</p>

            <div class="checklist">
                <h4>Advanced Features to Add:</h4>
                <ul>
                    <li>Full news scraping from 8+ sources</li>
                    <li>AI sentiment analysis with DeepSeek</li>
                    <li>Trading signal generation</li>
                    <li>Real-time price data from DEXes</li>
                    <li>WebSocket real-time updates</li>
                    <li>Beautiful dashboard interface</li>
                </ul>
            </div>

            <div class="info">
                To add these features, you'll need to copy the complete source files from your original project into this directory structure. The basic setup you just completed provides the foundation!
            </div>
        </div>
    </div>

    <script>
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(() => {
                // Show feedback
                event.target.style.background = '#10b981';
                event.target.textContent = 'Copied!';
                setTimeout(() => {
                    event.target.style.background = '#667eea';
                    event.target.textContent = 'Copy';
                }, 2000);
            });
        }

        // Define content for copy buttons
        const packageJsonContent = `{
  "name": "pulsechain-news-hub",
  "version": "1.0.0",
  "description": "AI-powered PulseChain news aggregator with trading signals",
  "main": "server.js",
  "scripts": {
    "start": "node server.js",
    "dev": "nodemon server.js",
    "test": "node test-connections.js"
  },
  "dependencies": {
    "express": "^4.18.2",
    "cors": "^2.8.5",
    "dotenv": "^16.3.1",
    "axios": "^1.6.0",
    "cheerio": "^1.0.0-rc.12",
    "node-cron": "^3.0.3",
    "better-sqlite3": "^9.2.2",
    "ws": "^8.14.2",
    "rss-parser": "^3.13.0",
    "express-rate-limit": "^7.1.5"
  },
  "devDependencies": {
    "nodemon": "^3.0.1"
  },
  "engines": {
    "node": ">=18.0.0"
  },
  "author": "PulseChain News Hub",
  "license": "MIT"
}`;

        const envContent = `# DeepSeek API Configuration
DEEPSEEK_API_KEY=sk-your-deepseek-api-key-here
DEEPSEEK_BASE_URL=https://api.deepseek.com

# Server Configuration
PORT=3001
NODE_ENV=development

# WebSocket Configuration
WS_PORT=3002

# Database Configuration
DB_PATH=./data/pulsechain_news.db

# News Scraping Configuration
SCRAPING_INTERVAL=300000
MAX_ARTICLES_PER_SOURCE=50

# DEX Data Configuration
PULSECHAIN_RPC=https://rpc.pulsechain.com
PULSEX_API=https://api.pulsex.com/api/v1
DEX_REFRESH_INTERVAL=60000

# Rate Limiting
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=100`;

        const serverContent = `const express = require('express');
const cors = require('cors');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3001;

app.use(cors());
app.use(express.json());

// Basic health check
app.get('/api/health', (req, res) => {
    res.json({
        status: 'healthy',
        message: 'PulseChain News Hub is running!',
        timestamp: new Date().toISOString(),
        version: '1.0.0'
    });
});

// Serve static files
app.use(express.static('.'));

// Basic dashboard route
app.get('/', (req, res) => {
    res.send(\`
        <html>
            <body style="font-family: Arial; padding: 40px; background: #f0f4f8;">
                <div style="max-width: 800px; margin: 0 auto; background: white; padding: 40px; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
                    <h1 style="color: #667eea; text-align: center;">🚀 PulseChain News Hub</h1>
                    <h2 style="color: #10b981; text-align: center;">Successfully Running!</h2>
                    <p style="font-size: 18px; text-align: center; color: #666;">
                        Your AI-powered news aggregator is up and running.<br>
                        DeepSeek API Key: \${process.env.DEEPSEEK_API_KEY ? '✅ Configured' : '❌ Missing'}
                    </p>
                    <div style="text-align: center; margin-top: 30px;">
                        <a href="/api/health" style="background: #667eea; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; margin: 10px;">API Health Check</a>
                    </div>
                </div>
            </body>
        </html>
    \`);
});

app.listen(PORT, () => {
    console.log(\`🚀 PulseChain News Hub running on http://localhost:\${PORT}\`);
    console.log(\`📊 API Health: http://localhost:\${PORT}/api/health\`);
    console.log(\`🔑 DeepSeek API: \${process.env.DEEPSEEK_API_KEY ? 'Configured ✅' : 'Missing ❌'}\`);
});`;

        const systemCheckContent = `#!/bin/bash
echo "🔍 PulseChain News Hub System Check"
echo "=================================="
echo "Node.js: $(node --version 2>/dev/null || echo 'Not installed ❌')"
echo "NPM: $(npm --version 2>/dev/null || echo 'Not installed ❌')"
echo "Project Dir: $(pwd)"
echo "Package.json: $([ -f package.json ] && echo 'Found ✅' || echo 'Missing ❌')"
echo ".env file: $([ -f .env ] && echo 'Found ✅' || echo 'Missing ❌')"
echo "node_modules: $([ -d node_modules ] && echo 'Installed ✅' || echo 'Missing - run npm install ❌')"
echo "Server running: $(curl -s http://localhost:3001/api/health > /dev/null && echo 'Yes ✅' || echo 'No ❌')"
echo "=================================="`;
    </script>
</body>
</html>