Can you make me a web app for collecting all the news on latest on anything happening in Pulsechain block chain, assets suggestions for further analasys, interesting outputs, I will use Deepseek API so include a prompt for Deepseek AI APi config in the webapplication, only the news, and then we will validate the sources, we will filter by trusted sources, and new sources, if any source has been wrong or shown manipulative trades, we should have a warning sign to them, let's see waht you can pull of, all the news and sentiment should be from free only sources, opensource sources, not free tier or anything that has limits or has a paywall behind something that is avaiulable for all, scrape all outlests for anything Pulsechain related, and present them as articles with graphs, numbers, suggestions, amount of impressions, amount of ppl talking about it, where most of the IP addresses releasing the news are from, 
