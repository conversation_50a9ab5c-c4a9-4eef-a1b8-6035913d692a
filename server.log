Starting PulseChain News Hub server...
Initializing database...
Connected to SQLite database
Database schema created successfully
Database initialized successfully
WebSocket server running on port 3002
Running initial analysis...
HTTP server running on port 3001
Dashboard: http://localhost:3001/dashboard
API: http://localhost:3001/api/health
WebSocket client connected
WebSocket message: { type: 'subscribe' }
Starting full analysis pipeline...
Step 1: Scraping news...
Starting news scraping from all sources...
Scraping CoinTelegraph...
Found 31 articles, 2 relevant from CoinTelegraph
Scraping CryptoNews...
Found 20 articles, 10 relevant from CryptoNews
Scraping Decrypt...
Found 56 articles, 19 relevant from Decrypt
Scraping CoinDesk...
Found 25 articles, 12 relevant from CoinDesk
Scraping BeInCrypto...
Found 12 articles, 7 relevant from BeInCrypto
Scraping CryptoSlate...
Found 10 articles, 5 relevant from CryptoSlate
Scraping AMBCrypto...
Found 16 articles, 2 relevant from AMBCrypto
Scraping NewsBTC...
Found 10 articles, 3 relevant from NewsBTC
Reddit scraping error: Status code 403
Step 2: Storing articles...
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Heres what happened in crypto today": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Trump-linked WLFIs 40% decline causes millions in losses for crypto whales: Finance Redefined": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Robinhood Soars on S&P 500 Inclusion as Strategy Gets Snubbed": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Crypto Price Prediction Today 5 September  XRP, Cardano, Shiba Inu": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Best Crypto to Buy Now  5 September": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Chinas DeepSeek AI Predicts the Price of XRP, Ethereum and Pi Coin by the End of 2025": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "SOL Strategies Takes Wall Street: First SOL-Focused Firm Wins Nasdaq Listing Approval": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Pi Coin Price Prediction: Bollinger Bands Explode Tight  Wyckoff Signals Incoming Face-Melter Rally": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "DOGE Price Prediction: Dogecoin Breaks $0.21 as Trump-Backed Thumzup Invests $50M in DOGE Mining Rigs  $1 Next?": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Weekly Crypto Regulation News: Fed Sets Stablecoin Showdown and Spot Trading Gains Momentum": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Hyperliquid Slashes Trading Fees by 80% Ahead of Native Stablecoin Launch": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "WLFI Price Prediction: Trump Token in Chaos After Billionaire Wallet Blacklisted  Collapse Incoming?": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Public Keys: Big ETH Stakes, AI Boost for Bitcoin Miners and 24/7 Trading": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "What is Ethereum (ETH)? A Beginner's Guide to the Smart Contract Blockchain": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "SharpLink Will Explore Staking Portion of $3.6 Billion Ethereum Treasury on Linea, CEO Says": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "BITCOIN BOUNCES, NFPS TODAY, WLFI BLACKLISTS JUSTIN SUN": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "SEC, CFTC Propose Making US Financial Markets 24/7 to Keep Up with Crypto": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Morning Minute: Stripe's L1 Blockchain 'Tempo' Goes Live (Sort Of)": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Bitcoin Whale Moves $52 Million in BTC After 13 Years": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "World Liberty Blacklists Justin Sun's Ethereum Wallet After Moving Millions in WLFI": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Stripe and Paradigm Reveal Tempo Blockchain, Built With Help From OpenAI and Visa": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "SOL LEADS, SEC SPEAKS ON CRYPTO GUIDANCE, TOKENISED RWAS HEAT UP": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "SOLANA HITS $211 & OUTPREFORMS CRYPTO MAJORS!": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "WHY IS CRYPTO DOWN? ETH LEADS CRYPTO LOWER, PCE INFLATION TODAY": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "ETHEREUM IS SENDING! IS SOLANA NEXT?": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "SOL LEADS MAJORS HIGHER, HYPE HITS ATH, XPL SHORT-SQUEEZE": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "BTC WHALE CAUSES MINI-CRASH, ETH HITS ATH, FOOTBALLFUN GOES VIRAL": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "KANYE LAUNCHES TOKEN, BNB ATH, CHINA CONSIDERING STABLECOINS": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "TOM LEE BUYS $1.7B ETH IN PAST WEEK, $LIGHT JUMPS 50% TO NEW ATH, CRYPTO MAJORS IN THE GREEN": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "STRATEGY BUYING BTC AGAIN, GOOGLE INVESTS MORE IN BTC MINING, $LIGHT SOARS": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "CRYPTO SLIDES, LINK LEADS ALTS, FED ENDS CRYPTO SUPERVISION": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Bitcoin and Stablecoins Dominate as India, U.S. Top 2025 Crypto Adoption Index": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "The Banks and the Unbanked: Blockchains Biggest Beneficiaries Sit at Both Ends of the Financial Spectrum": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "DOGE Flashes Bullish Signal as RSI Holds Neutral and Volume Surges": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Tokenization Offers Enhanced Liquidity, but Faces Major Hurdles, BofA Says": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Stripe CEO Patrick Collison Explains Why Businesses Are Turning to Stablecoins": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "State of Crypto: Congress Is Back From Break": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Adam Back Joins Fight for the Soul of Bitcoin Over 'JPEG Spam'": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Belarus Seeks to Cement Role as Crypto Digital Haven, President Lukashenko Says": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Legislation Steering U.S. Fate of Crypto Emerges in New Version in Senate": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Popular DEX Hyperliquid Moves Forward to Launch Proprietary Stablecoin": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "SOL Strategies Wins Nasdaq Listing, Shares to Trade Under STKE": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Ethereum Staking Queue Overtakes Exits as Fears of a Sell-off Subside": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Crypto Phishing Scams Rise 72% In August To Steal Over $12 Million": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Ethereum ETFs Suffer Second-Largest Daily Withdrawal Since Launch": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Arkham Finds $5 Billion In Bitcoin That Germany Could Still Claim": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Why is the Crypto Market Up Today?": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Stripes Tempo Blockchain: The New Libra or Ethereum Killer?": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "How Realistic Is Air Chinas XRP Payment Integration Plan?": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Senate Banking Committee Releases Draft of Market Structure Legislation": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Euro stablecoins are 0.15% of the market. Heres how Europe catches up": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "The AI economy needs new payment rails: How stablecoins and lightning fit the bill": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "From Jamie Dimon to Donald Trump: Why everyone eventually understands Bitcoin": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Ethena token rallies over 12% following StablecoinXs $530 million capital raise": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "SOL Strategies secures Nasdaq approval as institutional giants plan billion-dollar Solana treasury": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Cardano  Assessing if ADA bulls can break the $0.94 barrier": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Binance Coin  Why THESE drivers could fuel BNBs $1K rally": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Bitcoin Treasury Purchases Down Amid Record Holdings  What Does This Mean?": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Bitcoin Price Vs. BTC Treasury Companies: Interesting 1:4 Ratio Pops Up": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Ripples XRP Ledger Just Introduced A Pivotal Update In Its Quest For Dominance": SQLite3 can only bind numbers, strings, bigints, buffers, and null
Step 3: Fetching DEX data...
Fetching DEX data...
DEX data fetched successfully
Step 4: AI analysis...
Processing undefined articles with AI...
Step 5: Correlation analysis...
Analyzing news-price correlations...
Step 6: Updating analytics...
SQL Error: NOT NULL constraint failed: analytics.metric_value
SQL: 
            INSERT INTO analytics (metric_name, metric_value, metric_type, category, time_period)
            VALUES (?, ?, ?, ?, ?)
        
Analytics update error: SqliteError: NOT NULL constraint failed: analytics.metric_value
    at DatabaseService.run (/home/<USER>/Desktop/pulsechain_news_hub/src/database/database.js:81:33)
    at DatabaseService.insertAnalytic (/home/<USER>/Desktop/pulsechain_news_hub/src/database/database.js:389:21)
    at AnalyzerService.updateAnalytics (/home/<USER>/Desktop/pulsechain_news_hub/src/services/analyzer.js:361:28)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async AnalyzerService.runFullAnalysis (/home/<USER>/Desktop/pulsechain_news_hub/src/services/analyzer.js:61:13) {
  code: 'SQLITE_CONSTRAINT_NOTNULL'
}
Full analysis pipeline completed successfully
Results: {
  "success": true,
  "duration": 24136,
  "scraping": {
    "totalArticles": 60,
    "errors": 0
  },
  "storage": {
    "stored": 0,
    "duplicates": 0,
    "errors": 60
  },
  "analysis": {
    "processed": 0,
    "aiAnalyzed": 0,
    "signalsGenerated": 0
  },
  "correlation": {
    "correlations": [],
    "strength": 0,
    "confidence": 0
  },
  "dexData": {
    "tokens": 4,
    "healthScore": 100
  }
}
Initial analysis completed: Success
Running scheduled analysis...
Starting full analysis pipeline...
Step 1: Scraping news...
Starting news scraping from all sources...
Scraping CoinTelegraph...
Found 31 articles, 2 relevant from CoinTelegraph
Scraping CryptoNews...
Found 20 articles, 10 relevant from CryptoNews
Scraping Decrypt...
Found 56 articles, 19 relevant from Decrypt
Scraping CoinDesk...
Error scraping CoinDesk: Failed to scrape CoinDesk: Request timed out after 10000ms
Scraping BeInCrypto...
Found 12 articles, 7 relevant from BeInCrypto
Scraping CryptoSlate...
Found 10 articles, 5 relevant from CryptoSlate
Scraping AMBCrypto...
Found 16 articles, 2 relevant from AMBCrypto
Scraping NewsBTC...
Found 10 articles, 3 relevant from NewsBTC
Reddit scraping error: read ECONNRESET
Step 2: Storing articles...
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Heres what happened in crypto today": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Trump-linked WLFIs 40% decline causes millions in losses for crypto whales: Finance Redefined": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Robinhood Soars on S&P 500 Inclusion as Strategy Gets Snubbed": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Crypto Price Prediction Today 5 September  XRP, Cardano, Shiba Inu": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Best Crypto to Buy Now  5 September": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Chinas DeepSeek AI Predicts the Price of XRP, Ethereum and Pi Coin by the End of 2025": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "SOL Strategies Takes Wall Street: First SOL-Focused Firm Wins Nasdaq Listing Approval": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Pi Coin Price Prediction: Bollinger Bands Explode Tight  Wyckoff Signals Incoming Face-Melter Rally": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "DOGE Price Prediction: Dogecoin Breaks $0.21 as Trump-Backed Thumzup Invests $50M in DOGE Mining Rigs  $1 Next?": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Weekly Crypto Regulation News: Fed Sets Stablecoin Showdown and Spot Trading Gains Momentum": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Hyperliquid Slashes Trading Fees by 80% Ahead of Native Stablecoin Launch": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "WLFI Price Prediction: Trump Token in Chaos After Billionaire Wallet Blacklisted  Collapse Incoming?": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Public Keys: Big ETH Stakes, AI Boost for Bitcoin Miners and 24/7 Trading": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "What is Ethereum (ETH)? A Beginner's Guide to the Smart Contract Blockchain": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "SharpLink Will Explore Staking Portion of $3.6 Billion Ethereum Treasury on Linea, CEO Says": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "BITCOIN BOUNCES, NFPS TODAY, WLFI BLACKLISTS JUSTIN SUN": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "SEC, CFTC Propose Making US Financial Markets 24/7 to Keep Up with Crypto": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Morning Minute: Stripe's L1 Blockchain 'Tempo' Goes Live (Sort Of)": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Bitcoin Whale Moves $52 Million in BTC After 13 Years": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "World Liberty Blacklists Justin Sun's Ethereum Wallet After Moving Millions in WLFI": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Stripe and Paradigm Reveal Tempo Blockchain, Built With Help From OpenAI and Visa": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "SOL LEADS, SEC SPEAKS ON CRYPTO GUIDANCE, TOKENISED RWAS HEAT UP": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "SOLANA HITS $211 & OUTPREFORMS CRYPTO MAJORS!": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "WHY IS CRYPTO DOWN? ETH LEADS CRYPTO LOWER, PCE INFLATION TODAY": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "ETHEREUM IS SENDING! IS SOLANA NEXT?": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "SOL LEADS MAJORS HIGHER, HYPE HITS ATH, XPL SHORT-SQUEEZE": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "BTC WHALE CAUSES MINI-CRASH, ETH HITS ATH, FOOTBALLFUN GOES VIRAL": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "KANYE LAUNCHES TOKEN, BNB ATH, CHINA CONSIDERING STABLECOINS": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "TOM LEE BUYS $1.7B ETH IN PAST WEEK, $LIGHT JUMPS 50% TO NEW ATH, CRYPTO MAJORS IN THE GREEN": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "STRATEGY BUYING BTC AGAIN, GOOGLE INVESTS MORE IN BTC MINING, $LIGHT SOARS": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "CRYPTO SLIDES, LINK LEADS ALTS, FED ENDS CRYPTO SUPERVISION": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Crypto Phishing Scams Rise 72% In August To Steal Over $12 Million": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Ethereum ETFs Suffer Second-Largest Daily Withdrawal Since Launch": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Arkham Finds $5 Billion In Bitcoin That Germany Could Still Claim": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Why is the Crypto Market Up Today?": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Stripes Tempo Blockchain: The New Libra or Ethereum Killer?": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "How Realistic Is Air Chinas XRP Payment Integration Plan?": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Senate Banking Committee Releases Draft of Market Structure Legislation": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Euro stablecoins are 0.15% of the market. Heres how Europe catches up": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "The AI economy needs new payment rails: How stablecoins and lightning fit the bill": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "From Jamie Dimon to Donald Trump: Why everyone eventually understands Bitcoin": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Ethena token rallies over 12% following StablecoinXs $530 million capital raise": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "SOL Strategies secures Nasdaq approval as institutional giants plan billion-dollar Solana treasury": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Cardano  Assessing if ADA bulls can break the $0.94 barrier": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Binance Coin  Why THESE drivers could fuel BNBs $1K rally": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Bitcoin Treasury Purchases Down Amid Record Holdings  What Does This Mean?": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Bitcoin Price Vs. BTC Treasury Companies: Interesting 1:4 Ratio Pops Up": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Ripples XRP Ledger Just Introduced A Pivotal Update In Its Quest For Dominance": SQLite3 can only bind numbers, strings, bigints, buffers, and null
Step 3: Fetching DEX data...
Fetching DEX data...
DEX data fetched successfully
Step 4: AI analysis...
Processing undefined articles with AI...
Step 5: Correlation analysis...
Analyzing news-price correlations...
Step 6: Updating analytics...
Full analysis pipeline completed successfully
Results: {
  "success": true,
  "duration": 33727,
  "scraping": {
    "totalArticles": 48,
    "errors": 1
  },
  "storage": {
    "stored": 0,
    "duplicates": 0,
    "errors": 48
  },
  "analysis": {
    "processed": 0,
    "aiAnalyzed": 0,
    "signalsGenerated": 0
  },
  "correlation": {
    "correlations": [],
    "strength": 0,
    "confidence": 0
  },
  "dexData": {
    "tokens": 4,
    "healthScore": 100
  }
}
Running scheduled analysis...
Starting full analysis pipeline...
Step 1: Scraping news...
Starting news scraping from all sources...
Scraping CoinTelegraph...
Found 31 articles, 2 relevant from CoinTelegraph
Scraping CryptoNews...
Found 20 articles, 10 relevant from CryptoNews
Scraping Decrypt...
Found 56 articles, 19 relevant from Decrypt
Scraping CoinDesk...
Found 25 articles, 12 relevant from CoinDesk
Scraping BeInCrypto...
Found 12 articles, 7 relevant from BeInCrypto
Scraping CryptoSlate...
Found 10 articles, 5 relevant from CryptoSlate
Scraping AMBCrypto...
Found 16 articles, 2 relevant from AMBCrypto
Scraping NewsBTC...
Error scraping NewsBTC: Failed to scrape NewsBTC: Request timed out after 10000ms
Reddit scraping error: read ECONNRESET
Step 2: Storing articles...
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Heres what happened in crypto today": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Trump-linked WLFIs 40% decline causes millions in losses for crypto whales: Finance Redefined": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Robinhood Soars on S&P 500 Inclusion as Strategy Gets Snubbed": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Crypto Price Prediction Today 5 September  XRP, Cardano, Shiba Inu": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Best Crypto to Buy Now  5 September": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Chinas DeepSeek AI Predicts the Price of XRP, Ethereum and Pi Coin by the End of 2025": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "SOL Strategies Takes Wall Street: First SOL-Focused Firm Wins Nasdaq Listing Approval": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Pi Coin Price Prediction: Bollinger Bands Explode Tight  Wyckoff Signals Incoming Face-Melter Rally": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "DOGE Price Prediction: Dogecoin Breaks $0.21 as Trump-Backed Thumzup Invests $50M in DOGE Mining Rigs  $1 Next?": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Weekly Crypto Regulation News: Fed Sets Stablecoin Showdown and Spot Trading Gains Momentum": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Hyperliquid Slashes Trading Fees by 80% Ahead of Native Stablecoin Launch": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "WLFI Price Prediction: Trump Token in Chaos After Billionaire Wallet Blacklisted  Collapse Incoming?": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Public Keys: Big ETH Stakes, AI Boost for Bitcoin Miners and 24/7 Trading": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "What is Ethereum (ETH)? A Beginner's Guide to the Smart Contract Blockchain": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "SharpLink Will Explore Staking Portion of $3.6 Billion Ethereum Treasury on Linea, CEO Says": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "BITCOIN BOUNCES, NFPS TODAY, WLFI BLACKLISTS JUSTIN SUN": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "SEC, CFTC Propose Making US Financial Markets 24/7 to Keep Up with Crypto": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Morning Minute: Stripe's L1 Blockchain 'Tempo' Goes Live (Sort Of)": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Bitcoin Whale Moves $52 Million in BTC After 13 Years": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "World Liberty Blacklists Justin Sun's Ethereum Wallet After Moving Millions in WLFI": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Stripe and Paradigm Reveal Tempo Blockchain, Built With Help From OpenAI and Visa": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "SOL LEADS, SEC SPEAKS ON CRYPTO GUIDANCE, TOKENISED RWAS HEAT UP": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "SOLANA HITS $211 & OUTPREFORMS CRYPTO MAJORS!": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "WHY IS CRYPTO DOWN? ETH LEADS CRYPTO LOWER, PCE INFLATION TODAY": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "ETHEREUM IS SENDING! IS SOLANA NEXT?": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "SOL LEADS MAJORS HIGHER, HYPE HITS ATH, XPL SHORT-SQUEEZE": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "BTC WHALE CAUSES MINI-CRASH, ETH HITS ATH, FOOTBALLFUN GOES VIRAL": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "KANYE LAUNCHES TOKEN, BNB ATH, CHINA CONSIDERING STABLECOINS": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "TOM LEE BUYS $1.7B ETH IN PAST WEEK, $LIGHT JUMPS 50% TO NEW ATH, CRYPTO MAJORS IN THE GREEN": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "STRATEGY BUYING BTC AGAIN, GOOGLE INVESTS MORE IN BTC MINING, $LIGHT SOARS": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "CRYPTO SLIDES, LINK LEADS ALTS, FED ENDS CRYPTO SUPERVISION": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Bitcoin and Stablecoins Dominate as India, U.S. Top 2025 Crypto Adoption Index": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "The Banks and the Unbanked: Blockchains Biggest Beneficiaries Sit at Both Ends of the Financial Spectrum": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "DOGE Flashes Bullish Signal as RSI Holds Neutral and Volume Surges": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Tokenization Offers Enhanced Liquidity, but Faces Major Hurdles, BofA Says": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Stripe CEO Patrick Collison Explains Why Businesses Are Turning to Stablecoins": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "State of Crypto: Congress Is Back From Break": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Adam Back Joins Fight for the Soul of Bitcoin Over 'JPEG Spam'": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Belarus Seeks to Cement Role as Crypto Digital Haven, President Lukashenko Says": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Legislation Steering U.S. Fate of Crypto Emerges in New Version in Senate": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Popular DEX Hyperliquid Moves Forward to Launch Proprietary Stablecoin": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "SOL Strategies Wins Nasdaq Listing, Shares to Trade Under STKE": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Ethereum Staking Queue Overtakes Exits as Fears of a Sell-off Subside": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Crypto Phishing Scams Rise 72% In August To Steal Over $12 Million": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Ethereum ETFs Suffer Second-Largest Daily Withdrawal Since Launch": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Arkham Finds $5 Billion In Bitcoin That Germany Could Still Claim": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Why is the Crypto Market Up Today?": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Stripes Tempo Blockchain: The New Libra or Ethereum Killer?": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "How Realistic Is Air Chinas XRP Payment Integration Plan?": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Senate Banking Committee Releases Draft of Market Structure Legislation": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Euro stablecoins are 0.15% of the market. Heres how Europe catches up": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "The AI economy needs new payment rails: How stablecoins and lightning fit the bill": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "From Jamie Dimon to Donald Trump: Why everyone eventually understands Bitcoin": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Ethena token rallies over 12% following StablecoinXs $530 million capital raise": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "SOL Strategies secures Nasdaq approval as institutional giants plan billion-dollar Solana treasury": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Cardano  Assessing if ADA bulls can break the $0.94 barrier": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Binance Coin  Why THESE drivers could fuel BNBs $1K rally": SQLite3 can only bind numbers, strings, bigints, buffers, and null
Step 3: Fetching DEX data...
Fetching DEX data...
DEX data fetched successfully
Step 4: AI analysis...
Processing undefined articles with AI...
Step 5: Correlation analysis...
Analyzing news-price correlations...
Step 6: Updating analytics...
Full analysis pipeline completed successfully
Results: {
  "success": true,
  "duration": 34679,
  "scraping": {
    "totalArticles": 57,
    "errors": 1
  },
  "storage": {
    "stored": 0,
    "duplicates": 0,
    "errors": 57
  },
  "analysis": {
    "processed": 0,
    "aiAnalyzed": 0,
    "signalsGenerated": 0
  },
  "correlation": {
    "correlations": [],
    "strength": 0,
    "confidence": 0
  },
  "dexData": {
    "tokens": 4,
    "healthScore": 100
  }
}
Running scheduled analysis...
Starting full analysis pipeline...
Step 1: Scraping news...
Starting news scraping from all sources...
Scraping CoinTelegraph...
Found 31 articles, 2 relevant from CoinTelegraph
Scraping CryptoNews...
Found 20 articles, 10 relevant from CryptoNews
Scraping Decrypt...
Found 56 articles, 19 relevant from Decrypt
Scraping CoinDesk...
Found 25 articles, 12 relevant from CoinDesk
Scraping BeInCrypto...
Found 12 articles, 7 relevant from BeInCrypto
Scraping CryptoSlate...
Found 10 articles, 5 relevant from CryptoSlate
Scraping AMBCrypto...
Found 16 articles, 2 relevant from AMBCrypto
Scraping NewsBTC...
Found 10 articles, 3 relevant from NewsBTC
Reddit scraping error: Status code 403
Step 2: Storing articles...
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Heres what happened in crypto today": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Trump-linked WLFIs 40% decline causes millions in losses for crypto whales: Finance Redefined": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Robinhood Soars on S&P 500 Inclusion as Strategy Gets Snubbed": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Crypto Price Prediction Today 5 September  XRP, Cardano, Shiba Inu": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Best Crypto to Buy Now  5 September": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Chinas DeepSeek AI Predicts the Price of XRP, Ethereum and Pi Coin by the End of 2025": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "SOL Strategies Takes Wall Street: First SOL-Focused Firm Wins Nasdaq Listing Approval": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Pi Coin Price Prediction: Bollinger Bands Explode Tight  Wyckoff Signals Incoming Face-Melter Rally": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "DOGE Price Prediction: Dogecoin Breaks $0.21 as Trump-Backed Thumzup Invests $50M in DOGE Mining Rigs  $1 Next?": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Weekly Crypto Regulation News: Fed Sets Stablecoin Showdown and Spot Trading Gains Momentum": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Hyperliquid Slashes Trading Fees by 80% Ahead of Native Stablecoin Launch": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "WLFI Price Prediction: Trump Token in Chaos After Billionaire Wallet Blacklisted  Collapse Incoming?": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Public Keys: Big ETH Stakes, AI Boost for Bitcoin Miners and 24/7 Trading": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "What is Ethereum (ETH)? A Beginner's Guide to the Smart Contract Blockchain": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "SharpLink Will Explore Staking Portion of $3.6 Billion Ethereum Treasury on Linea, CEO Says": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "BITCOIN BOUNCES, NFPS TODAY, WLFI BLACKLISTS JUSTIN SUN": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "SEC, CFTC Propose Making US Financial Markets 24/7 to Keep Up with Crypto": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Morning Minute: Stripe's L1 Blockchain 'Tempo' Goes Live (Sort Of)": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Bitcoin Whale Moves $52 Million in BTC After 13 Years": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "World Liberty Blacklists Justin Sun's Ethereum Wallet After Moving Millions in WLFI": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Stripe and Paradigm Reveal Tempo Blockchain, Built With Help From OpenAI and Visa": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "SOL LEADS, SEC SPEAKS ON CRYPTO GUIDANCE, TOKENISED RWAS HEAT UP": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "SOLANA HITS $211 & OUTPREFORMS CRYPTO MAJORS!": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "WHY IS CRYPTO DOWN? ETH LEADS CRYPTO LOWER, PCE INFLATION TODAY": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "ETHEREUM IS SENDING! IS SOLANA NEXT?": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "SOL LEADS MAJORS HIGHER, HYPE HITS ATH, XPL SHORT-SQUEEZE": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "BTC WHALE CAUSES MINI-CRASH, ETH HITS ATH, FOOTBALLFUN GOES VIRAL": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "KANYE LAUNCHES TOKEN, BNB ATH, CHINA CONSIDERING STABLECOINS": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "TOM LEE BUYS $1.7B ETH IN PAST WEEK, $LIGHT JUMPS 50% TO NEW ATH, CRYPTO MAJORS IN THE GREEN": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "STRATEGY BUYING BTC AGAIN, GOOGLE INVESTS MORE IN BTC MINING, $LIGHT SOARS": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "CRYPTO SLIDES, LINK LEADS ALTS, FED ENDS CRYPTO SUPERVISION": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Bitcoin and Stablecoins Dominate as India, U.S. Top 2025 Crypto Adoption Index": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "The Banks and the Unbanked: Blockchains Biggest Beneficiaries Sit at Both Ends of the Financial Spectrum": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "DOGE Flashes Bullish Signal as RSI Holds Neutral and Volume Surges": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Tokenization Offers Enhanced Liquidity, but Faces Major Hurdles, BofA Says": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Stripe CEO Patrick Collison Explains Why Businesses Are Turning to Stablecoins": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "State of Crypto: Congress Is Back From Break": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Adam Back Joins Fight for the Soul of Bitcoin Over 'JPEG Spam'": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Belarus Seeks to Cement Role as Crypto Digital Haven, President Lukashenko Says": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Legislation Steering U.S. Fate of Crypto Emerges in New Version in Senate": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Popular DEX Hyperliquid Moves Forward to Launch Proprietary Stablecoin": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "SOL Strategies Wins Nasdaq Listing, Shares to Trade Under STKE": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Ethereum Staking Queue Overtakes Exits as Fears of a Sell-off Subside": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Crypto Phishing Scams Rise 72% In August To Steal Over $12 Million": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Ethereum ETFs Suffer Second-Largest Daily Withdrawal Since Launch": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Arkham Finds $5 Billion In Bitcoin That Germany Could Still Claim": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Why is the Crypto Market Up Today?": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Stripes Tempo Blockchain: The New Libra or Ethereum Killer?": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "How Realistic Is Air Chinas XRP Payment Integration Plan?": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Senate Banking Committee Releases Draft of Market Structure Legislation": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Euro stablecoins are 0.15% of the market. Heres how Europe catches up": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "The AI economy needs new payment rails: How stablecoins and lightning fit the bill": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "From Jamie Dimon to Donald Trump: Why everyone eventually understands Bitcoin": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Ethena token rallies over 12% following StablecoinXs $530 million capital raise": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "SOL Strategies secures Nasdaq approval as institutional giants plan billion-dollar Solana treasury": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Cardano  Assessing if ADA bulls can break the $0.94 barrier": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Binance Coin  Why THESE drivers could fuel BNBs $1K rally": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Bitcoin Treasury Purchases Down Amid Record Holdings  What Does This Mean?": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Bitcoin Price Vs. BTC Treasury Companies: Interesting 1:4 Ratio Pops Up": SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL Error: SQLite3 can only bind numbers, strings, bigints, buffers, and null
SQL: 
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        
Error storing article "Ripples XRP Ledger Just Introduced A Pivotal Update In Its Quest For Dominance": SQLite3 can only bind numbers, strings, bigints, buffers, and null
Step 3: Fetching DEX data...
Fetching DEX data...
DEX data fetched successfully
Step 4: AI analysis...
Processing undefined articles with AI...
Step 5: Correlation analysis...
Analyzing news-price correlations...
Step 6: Updating analytics...
Full analysis pipeline completed successfully
Results: {
  "success": true,
  "duration": 28742,
  "scraping": {
    "totalArticles": 60,
    "errors": 0
  },
  "storage": {
    "stored": 0,
    "duplicates": 0,
    "errors": 60
  },
  "analysis": {
    "processed": 0,
    "aiAnalyzed": 0,
    "signalsGenerated": 0
  },
  "correlation": {
    "correlations": [],
    "strength": 0,
    "confidence": 0
  },
  "dexData": {
    "tokens": 4,
    "healthScore": 100
  }
}
