/**
 * News Scraping Service
 * Aggregates crypto and PulseChain news from multiple free sources
 */

const RSSParser = require('rss-parser');
const axios = require('axios');
const cheerio = require('cheerio');
const deepseek = require('./deepseek');

class NewsScrapingService {
    constructor() {
        this.rssParser = new RSSParser({
            timeout: 10000,
            customFields: {
                item: [
                    ['media:content', 'mediaContent'],
                    ['content:encoded', 'fullContent']
                ]
            }
        });

        // Free RSS sources for crypto news
        this.sources = [
            {
                name: 'CoinTelegraph',
                url: 'https://cointelegraph.com/rss',
                type: 'rss',
                credibility: 8.5,
                tags: ['general', 'analysis']
            },
            {
                name: 'CryptoNews',
                url: 'https://cryptonews.com/news/feed/',
                type: 'rss',
                credibility: 7.8,
                tags: ['general', 'breaking']
            },
            {
                name: 'Decrypt',
                url: 'https://decrypt.co/feed',
                type: 'rss',
                credibility: 8.2,
                tags: ['defi', 'technology']
            },
            {
                name: 'CoinDesk',
                url: 'https://www.coindesk.com/arc/outboundfeeds/rss/',
                type: 'rss',
                credibility: 9.0,
                tags: ['institutional', 'analysis']
            },
            {
                name: 'BeInCrypto',
                url: 'https://beincrypto.com/feed/',
                type: 'rss',
                credibility: 7.5,
                tags: ['altcoins', 'defi']
            },
            {
                name: 'CryptoSlate',
                url: 'https://cryptoslate.com/feed/',
                type: 'rss',
                credibility: 8.0,
                tags: ['analysis', 'projects']
            },
            {
                name: 'AMBCrypto',
                url: 'https://ambcrypto.com/feed/',
                type: 'rss',
                credibility: 7.2,
                tags: ['analysis', 'market']
            },
            {
                name: 'NewsBTC',
                url: 'https://www.newsbtc.com/feed/',
                type: 'rss',
                credibility: 7.8,
                tags: ['technical', 'trading']
            }
        ];

        this.pulseChainKeywords = [
            'pulsechain', 'pls', 'pulsex', 'hex', 'richard heart',
            'prc-20', 'pulse defi', 'phex', 'plsx',
            // Broader crypto terms that might include PulseChain mentions
            'ethereum killer', 'eth alternative', 'layer 1', 'blockchain',
            'defi', 'decentralized finance', 'staking', 'yield farming',
            'bridge', 'cross-chain', 'evm compatible', 'proof of stake'
        ];
    }

    /**
     * Scrape all configured news sources
     */
    async scrapeAllSources() {
        console.log('Starting news scraping from all sources...');
        const allArticles = [];
        const errors = [];

        for (const source of this.sources) {
            try {
                console.log(`Scraping ${source.name}...`);
                const articles = await this.scrapeSource(source);
                
                // Filter for PulseChain relevance (temporarily less restrictive)
                let relevantArticles = articles.filter(article => 
                    this.isPulseChainRelated(article)
                );
                
                // If no PulseChain-specific articles found, include some general crypto news
                if (relevantArticles.length === 0 && articles.length > 0) {
                    // Take up to 3 general crypto articles and tag them for review
                    relevantArticles = articles.slice(0, 3).map(article => ({
                        ...article,
                        needsReview: true,
                        reviewNote: 'General crypto article - check for PulseChain relevance'
                    }));
                    console.log(`No PulseChain articles found, including ${relevantArticles.length} general crypto articles from ${source.name}`);
                }

                console.log(`Found ${articles.length} articles, ${relevantArticles.length} relevant from ${source.name}`);
                
                allArticles.push(...relevantArticles.map(article => ({
                    ...article,
                    source: source.name,
                    sourceCredibility: source.credibility,
                    sourceTags: source.tags,
                    scrapedAt: new Date().toISOString()
                })));

            } catch (error) {
                console.error(`Error scraping ${source.name}:`, error.message);
                errors.push({
                    source: source.name,
                    error: error.message
                });
            }

            // Rate limiting - wait between sources
            await this.sleep(2000);
        }

        // Additional scraping for PulseChain specific content
        try {
            const socialArticles = await this.scrapeSocialMedia();
            allArticles.push(...socialArticles);
        } catch (error) {
            console.error('Social media scraping error:', error.message);
        }

        return {
            articles: allArticles,
            errors,
            summary: {
                total: allArticles.length,
                sources: this.sources.length,
                errors: errors.length,
                scrapedAt: new Date().toISOString()
            }
        };
    }

    /**
     * Scrape individual RSS source
     */
    async scrapeSource(source) {
        try {
            const feed = await this.rssParser.parseURL(source.url);
            
            return feed.items.map(item => ({
                sourceId: source.id,
                title: this.cleanText(item.title),
                content: this.extractContent(item),
                description: this.cleanText(item.contentSnippet || item.description),
                url: item.link,
                publishedAt: new Date(item.pubDate || item.isoDate || Date.now()).toISOString(),
                author: item.creator || item.author || 'Unknown',
                categories: item.categories || [],
                imageUrl: this.extractImage(item),
                guid: item.guid || item.link
            }));

        } catch (error) {
            throw new Error(`Failed to scrape ${source.name}: ${error.message}`);
        }
    }

    /**
     * Check if article is PulseChain related
     */
    isPulseChainRelated(article) {
        const searchText = `${article.title} ${article.content} ${article.description}`.toLowerCase();
        
        return this.pulseChainKeywords.some(keyword => 
            searchText.includes(keyword.toLowerCase())
        );
    }

    /**
     * Extract full content from article
     */
    extractContent(item) {
        // Try different content fields
        let content = item.fullContent || item['content:encoded'] || item.content || item.description || '';
        
        // Clean HTML tags
        const $ = cheerio.load(content);
        content = $.text();
        
        return this.cleanText(content);
    }

    /**
     * Extract image from article
     */
    extractImage(item) {
        // Try different image fields
        if (item.mediaContent && item.mediaContent.$ && item.mediaContent.$.url) {
            return item.mediaContent.$.url;
        }
        
        if (item.enclosure && item.enclosure.url) {
            return item.enclosure.url;
        }

        // Parse content for images
        const content = item.fullContent || item.content || item.description || '';
        const $ = cheerio.load(content);
        const img = $('img').first();
        
        if (img.length) {
            return img.attr('src');
        }

        return null;
    }

    /**
     * Scrape social media and forums for PulseChain mentions
     */
    async scrapeSocialMedia() {
        const socialArticles = [];
        
        try {
            // Reddit PulseChain mentions (using RSS)
            const redditFeed = await this.rssParser.parseURL('https://www.reddit.com/r/PulseChain/.rss');
            
            redditFeed.items.forEach(item => {
                socialArticles.push({
                    sourceId: 9, // Reddit PulseChain source ID from schema
                    title: this.cleanText(item.title),
                    content: this.cleanText(item.contentSnippet || ''),
                    description: this.cleanText(item.contentSnippet || ''),
                    url: item.link,
                    publishedAt: new Date(item.pubDate || Date.now()).toISOString(),
                    author: item.author || 'Reddit User',
                    categories: ['social', 'reddit'],
                    imageUrl: null,
                    guid: item.guid,
                    source: 'Reddit PulseChain',
                    sourceCredibility: 6.5,
                    sourceTags: ['community', 'social'],
                    scrapedAt: new Date().toISOString()
                });
            });

        } catch (error) {
            console.error('Reddit scraping error:', error.message);
        }

        return socialArticles;
    }

    /**
     * Enhanced article processing with AI analysis
     */
    async processArticles(articles, useAI = true) {
        console.log(`Processing ${articles.length} articles with AI analysis...`);
        
        const processedArticles = [];
        let aiAnalysisCount = 0;

        for (const article of articles) {
            try {
                let processedArticle = { ...article };

                if (useAI && aiAnalysisCount < 20) { // Limit AI calls to manage costs
                    const analysis = await deepseek.analyzeArticle(article);
                    
                    if (analysis.success) {
                        processedArticle = {
                            ...processedArticle,
                            aiAnalysis: analysis.analysis,
                            sentiment: analysis.analysis.sentiment,
                            tradingSignal: analysis.analysis.tradingSignal,
                            credibilityCheck: analysis.analysis.credibility,
                            processedAt: new Date().toISOString()
                        };
                        aiAnalysisCount++;
                    } else {
                        // Use fallback analysis
                        processedArticle = {
                            ...processedArticle,
                            aiAnalysis: analysis.fallback,
                            sentiment: analysis.fallback.sentiment,
                            processedAt: new Date().toISOString()
                        };
                    }
                } else {
                    // Basic sentiment analysis without AI
                    processedArticle.sentiment = this.basicSentimentAnalysis(article);
                }

                processedArticles.push(processedArticle);

                // Rate limiting for API calls
                if (aiAnalysisCount > 0) {
                    await this.sleep(1000);
                }

            } catch (error) {
                console.error(`Error processing article "${article.title}":`, error.message);
                
                // Add article without AI analysis
                processedArticles.push({
                    ...article,
                    sentiment: this.basicSentimentAnalysis(article),
                    processedAt: new Date().toISOString(),
                    processingError: error.message
                });
            }
        }

        return {
            articles: processedArticles,
            aiAnalysisUsed: aiAnalysisCount,
            totalProcessed: processedArticles.length
        };
    }

    /**
     * Basic sentiment analysis fallback
     */
    basicSentimentAnalysis(article) {
        const text = `${article.title} ${article.content}`.toLowerCase();
        
        const positiveWords = ['surge', 'bull', 'gain', 'rise', 'up', 'growth', 'positive', 'breakthrough', 'milestone', 'success'];
        const negativeWords = ['crash', 'bear', 'drop', 'down', 'fall', 'negative', 'concern', 'risk', 'problem', 'issue'];
        
        let positiveCount = 0;
        let negativeCount = 0;
        
        positiveWords.forEach(word => {
            if (text.includes(word)) positiveCount++;
        });
        
        negativeWords.forEach(word => {
            if (text.includes(word)) negativeCount++;
        });
        
        let label = 'neutral';
        let score = 50;
        
        if (positiveCount > negativeCount) {
            label = 'positive';
            score = Math.min(50 + (positiveCount * 10), 100);
        } else if (negativeCount > positiveCount) {
            label = 'negative';
            score = Math.max(50 - (negativeCount * 10), 0);
        }
        
        return {
            score,
            label,
            confidence: 0.6 // Lower confidence for basic analysis
        };
    }

    /**
     * Clean and normalize text content
     */
    cleanText(text) {
        if (!text) return '';
        
        return text
            .replace(/\s+/g, ' ')
            .replace(/[^\x00-\x7F]/g, '')
            .trim()
            .substring(0, 5000); // Limit content length
    }

    /**
     * Utility function for delays
     */
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * Get scraping statistics
     */
    getScrapingStats() {
        return {
            sources: this.sources.length,
            keywords: this.pulseChainKeywords.length,
            averageCredibility: this.sources.reduce((sum, s) => sum + s.credibility, 0) / this.sources.length,
            sourceTypes: ['rss', 'social'],
            lastUpdate: new Date().toISOString()
        };
    }
}

module.exports = new NewsScrapingService();