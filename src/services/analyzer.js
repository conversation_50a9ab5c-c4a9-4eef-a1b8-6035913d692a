/**
 * AI Analysis Pipeline Service
 * Orchestrates news processing, analysis, and signal generation
 */

const deepseek = require('./deepseek');
const database = require('../database/database');
const scraper = require('./scraper');
const dexService = require('./dex');

class AnalyzerService {
    constructor() {
        this.isRunning = false;
        this.processingQueue = [];
        this.maxConcurrentAnalysis = 3;
        this.analysisStats = {
            totalProcessed: 0,
            successCount: 0,
            errorCount: 0,
            lastRun: null
        };
    }

    /**
     * Start the complete analysis pipeline
     */
    async runFullAnalysis() {
        if (this.isRunning) {
            console.log('Analysis pipeline already running');
            return { success: false, message: 'Pipeline already running' };
        }

        this.isRunning = true;
        console.log('Starting full analysis pipeline...');

        try {
            const startTime = Date.now();

            // Step 1: Scrape fresh news
            console.log('Step 1: Scraping news...');
            const scrapingResult = await scraper.scrapeAllSources();
            
            // Step 2: Store articles in database
            console.log('Step 2: Storing articles...');
            const storedArticles = await this.storeArticles(scrapingResult.articles);

            // Step 3: Get DEX data for context
            console.log('Step 3: Fetching DEX data...');
            const dexData = await dexService.getDexData();
            
            // Step 4: Process articles with AI analysis
            console.log('Step 4: AI analysis...');
            const analysisResults = await this.processArticlesWithAI(storedArticles, dexData);

            // Step 5: Generate correlation analysis
            console.log('Step 5: Correlation analysis...');
            const correlationResults = await this.analyzeNewsCorrelations(analysisResults.processedArticles, dexData);

            // Step 6: Update analytics
            console.log('Step 6: Updating analytics...');
            await this.updateAnalytics(analysisResults, correlationResults, dexData);

            const duration = Date.now() - startTime;
            
            const result = {
                success: true,
                duration: duration,
                scraping: {
                    totalArticles: scrapingResult.articles.length,
                    errors: scrapingResult.errors.length
                },
                storage: {
                    stored: storedArticles.stored,
                    duplicates: storedArticles.duplicates,
                    errors: storedArticles.errors
                },
                analysis: {
                    processed: analysisResults.processed,
                    aiAnalyzed: analysisResults.aiAnalyzed,
                    signalsGenerated: analysisResults.signalsGenerated
                },
                correlation: correlationResults,
                dexData: {
                    tokens: Object.keys(dexData.prices || {}).length,
                    healthScore: dexData.health?.score || 0
                }
            };

            this.analysisStats.lastRun = new Date().toISOString();
            this.analysisStats.totalProcessed += result.analysis.processed;
            this.analysisStats.successCount++;

            console.log('Full analysis pipeline completed successfully');
            console.log('Results:', JSON.stringify(result, null, 2));

            return result;

        } catch (error) {
            console.error('Analysis pipeline error:', error);
            this.analysisStats.errorCount++;
            
            return {
                success: false,
                error: error.message,
                duration: Date.now() - (this.startTime || Date.now())
            };

        } finally {
            this.isRunning = false;
        }
    }

    /**
     * Store scraped articles in database
     */
    async storeArticles(articles) {
        const results = { stored: 0, duplicates: 0, errors: 0 };
        
        for (const article of articles) {
            try {
                // Check for duplicates
                const exists = await database.articleExists(article.url);
                if (exists) {
                    results.duplicates++;
                    continue;
                }

                // Get source ID
                const sources = await database.getSources();
                const source = sources.find(s => s.name === article.source);
                if (!source) {
                    console.warn(`Unknown source: ${article.source}`);
                    continue;
                }

                // Insert article
                const insertResult = await database.insertArticle({
                    sourceId: source.id,
                    title: article.title,
                    content: article.content,
                    description: article.description,
                    url: article.url,
                    publishedAt: article.publishedAt,
                    author: article.author,
                    imageUrl: article.imageUrl,
                    guid: article.guid,
                    categories: article.categories
                });

                article.id = insertResult.id; // Add ID for further processing
                results.stored++;

            } catch (error) {
                console.error(`Error storing article "${article.title}":`, error.message);
                results.errors++;
            }
        }

        return results;
    }

    /**
     * Process articles with AI analysis
     */
    async processArticlesWithAI(articles, dexContext = {}) {
        console.log(`Processing ${articles.length} articles with AI...`);
        
        const processedArticles = [];
        let aiAnalyzed = 0;
        let signalsGenerated = 0;

        // Limit AI analysis to manage costs
        const maxAIAnalysis = Math.min(articles.length, 15);
        
        for (let i = 0; i < articles.length; i++) {
            const article = articles[i];
            
            try {
                let analysis = null;
                
                // Use AI analysis for most important articles
                if (i < maxAIAnalysis) {
                    const aiResult = await deepseek.analyzeArticle(article);
                    
                    if (aiResult.success) {
                        analysis = aiResult.analysis;
                        aiAnalyzed++;
                        
                        // Store AI analysis in database
                        if (article.id) {
                            await database.updateArticleAnalysis(article.id, analysis);
                        }
                        
                        // Generate trading signal if confidence is high
                        if (analysis.tradingSignal && analysis.tradingSignal.confidence > 0.7) {
                            await this.generateTradingSignal(article, analysis, dexContext);
                            signalsGenerated++;
                        }
                        
                    } else {
                        // Fallback to basic analysis
                        analysis = aiResult.fallback;
                    }
                } else {
                    // Basic sentiment analysis for remaining articles
                    analysis = {
                        sentiment: scraper.basicSentimentAnalysis(article),
                        tradingSignal: { action: 'HOLD', confidence: 0.3 }
                    };
                }

                processedArticles.push({
                    ...article,
                    analysis
                });

                // Rate limiting
                if (aiAnalyzed > 0) {
                    await this.sleep(1000);
                }

            } catch (error) {
                console.error(`Error processing article "${article.title}":`, error.message);
                
                // Add article with basic analysis
                processedArticles.push({
                    ...article,
                    analysis: {
                        sentiment: { score: 50, label: 'neutral', confidence: 0 },
                        tradingSignal: { action: 'HOLD', confidence: 0 }
                    },
                    processingError: error.message
                });
            }
        }

        return {
            processedArticles,
            processed: processedArticles.length,
            aiAnalyzed,
            signalsGenerated
        };
    }

    /**
     * Generate trading signal from analysis
     */
    async generateTradingSignal(article, analysis, dexContext) {
        try {
            const signal = {
                articleId: article.id,
                tokenSymbol: 'PLS', // Default to PLS, could be enhanced to detect specific tokens
                signalType: analysis.tradingSignal.action,
                confidence: analysis.tradingSignal.confidence,
                timeframe: analysis.tradingSignal.timeframe || 'medium',
                reasoning: analysis.tradingSignal.reasoning,
                riskLevel: this.calculateRiskLevel(analysis),
                marketSentiment: analysis.sentiment.label,
                newsCatalyst: article.title,
                expiresAt: this.calculateSignalExpiry(analysis.tradingSignal.timeframe)
            };

            // Add price targets if DEX data is available
            if (dexContext.prices && dexContext.prices.PLS) {
                const currentPrice = dexContext.prices.PLS.price;
                signal.entryPrice = currentPrice;
                
                if (analysis.tradingSignal.action === 'BUY') {
                    signal.targetPrice = currentPrice * 1.1; // 10% target
                    signal.stopLoss = currentPrice * 0.95; // 5% stop loss
                } else if (analysis.tradingSignal.action === 'SELL') {
                    signal.targetPrice = currentPrice * 0.9; // 10% down target
                    signal.stopLoss = currentPrice * 1.05; // 5% stop loss
                }
            }

            await database.insertTradingSignal(signal);
            return signal;

        } catch (error) {
            console.error('Trading signal generation error:', error);
            return null;
        }
    }

    /**
     * Analyze correlations between news and price movements
     */
    async analyzeNewsCorrelations(articles, dexData) {
        try {
            console.log('Analyzing news-price correlations...');
            
            // Get recent price history
            const priceHistory = await database.getPriceHistory('PLS', 24);
            
            if (articles.length === 0 || priceHistory.length === 0) {
                return { correlations: [], strength: 0, confidence: 0 };
            }

            // Use DeepSeek to analyze correlations
            const correlationAnalysis = await deepseek.analyzeCorrelation(articles, priceHistory);
            
            if (correlationAnalysis.success) {
                // Store correlation insights
                for (const article of articles) {
                    if (article.id && article.analysis?.sentiment) {
                        await database.run(`
                            INSERT INTO news_correlations (
                                article_id, token_symbol, correlation_strength, 
                                time_lag_minutes, price_impact_percent
                            ) VALUES (?, ?, ?, ?, ?)
                        `, [
                            article.id,
                            'PLS',
                            correlationAnalysis.correlation?.strength || 0,
                            this.calculateTimeLag(article, priceHistory),
                            this.calculatePriceImpact(article, priceHistory)
                        ]);
                    }
                }

                return correlationAnalysis.correlation;
            } else {
                return { correlations: [], strength: 0, confidence: 0 };
            }

        } catch (error) {
            console.error('Correlation analysis error:', error);
            return { correlations: [], strength: 0, confidence: 0 };
        }
    }

    /**
     * Update analytics metrics
     */
    async updateAnalytics(analysisResults, correlationResults, dexData) {
        try {
            const timestamp = new Date().toISOString();

            // Analysis metrics
            await database.insertAnalytic('articles_processed', analysisResults.processed, 'count', 'analysis');
            await database.insertAnalytic('ai_analysis_used', analysisResults.aiAnalyzed, 'count', 'analysis');
            await database.insertAnalytic('signals_generated', analysisResults.signalsGenerated, 'count', 'trading');

            // Market metrics
            if (dexData.health) {
                await database.insertAnalytic('market_health_score', dexData.health.score, 'score', 'market');
            }

            if (dexData.volume) {
                await database.insertAnalytic('total_volume_24h', dexData.volume.total24h, 'count', 'market');
            }

            // Correlation metrics
            if (correlationResults.strength !== undefined) {
                await database.insertAnalytic('news_price_correlation', correlationResults.strength, 'score', 'correlation');
            }

            // System performance metrics
            await database.insertAnalytic('pipeline_runs', 1, 'count', 'system');
            await database.insertAnalytic('success_rate', this.analysisStats.successCount / (this.analysisStats.successCount + this.analysisStats.errorCount), 'rate', 'system');

        } catch (error) {
            console.error('Analytics update error:', error);
        }
    }

    /**
     * Get analysis pipeline status
     */
    getStatus() {
        return {
            isRunning: this.isRunning,
            queueLength: this.processingQueue.length,
            stats: this.analysisStats,
            lastRun: this.analysisStats.lastRun
        };
    }

    /**
     * Helper functions
     */
    calculateRiskLevel(analysis) {
        const confidence = analysis.tradingSignal?.confidence || 0;
        const manipulationSeverity = analysis.manipulationFlags?.severity || 'low';
        
        if (confidence > 0.8 && manipulationSeverity === 'low') {
            return 'low';
        } else if (confidence > 0.6 && manipulationSeverity !== 'high') {
            return 'medium';
        } else {
            return 'high';
        }
    }

    calculateSignalExpiry(timeframe) {
        const now = new Date();
        switch (timeframe) {
            case 'short':
                return new Date(now.getTime() + 6 * 60 * 60 * 1000); // 6 hours
            case 'medium':
                return new Date(now.getTime() + 24 * 60 * 60 * 1000); // 24 hours
            case 'long':
                return new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000); // 7 days
            default:
                return new Date(now.getTime() + 24 * 60 * 60 * 1000); // 24 hours
        }
    }

    calculateTimeLag(article, priceHistory) {
        // Simple calculation - would need more sophisticated analysis in production
        const articleTime = new Date(article.publishedAt);
        const relevantPricePoint = priceHistory.find(p => new Date(p.timestamp) > articleTime);
        
        if (relevantPricePoint) {
            return Math.round((new Date(relevantPricePoint.timestamp) - articleTime) / (1000 * 60)); // minutes
        }
        
        return 0;
    }

    calculatePriceImpact(article, priceHistory) {
        // Simple calculation - would need more sophisticated analysis in production
        const sentiment = article.analysis?.sentiment?.score || 50;
        
        // Rough estimate based on sentiment
        if (sentiment > 70) {
            return Math.random() * 5 + 1; // 1-6% positive impact
        } else if (sentiment < 30) {
            return -(Math.random() * 5 + 1); // 1-6% negative impact
        } else {
            return (Math.random() - 0.5) * 2; // -1% to 1%
        }
    }

    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * Run targeted analysis on specific articles
     */
    async analyzeSpecificArticles(articleIds) {
        try {
            const articles = await Promise.all(
                articleIds.map(id => database.get('SELECT * FROM articles WHERE id = ?', [id]))
            );
            
            const validArticles = articles.filter(a => a !== undefined);
            const dexData = await dexService.getDexData();
            
            return await this.processArticlesWithAI(validArticles, dexData);
            
        } catch (error) {
            console.error('Specific article analysis error:', error);
            throw error;
        }
    }

    /**
     * Cleanup old analysis data
     */
    async cleanup() {
        try {
            return await database.cleanup(30); // Clean data older than 30 days
        } catch (error) {
            console.error('Analysis cleanup error:', error);
            throw error;
        }
    }
}

module.exports = new AnalyzerService();