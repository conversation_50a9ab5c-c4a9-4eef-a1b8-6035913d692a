/**
 * DeepSeek AI Integration Service
 * Handles sentiment analysis, trading signals, and content enhancement
 */

const axios = require('axios');
require('dotenv').config();

class DeepSeekService {
    constructor() {
        this.apiKey = process.env.DEEPSEEK_API_KEY;
        this.baseUrl = process.env.DEEPSEEK_BASE_URL || 'https://api.deepseek.com';
        this.client = axios.create({
            baseURL: this.baseUrl,
            headers: {
                'Authorization': `Bearer ${this.apiKey}`,
                'Content-Type': 'application/json'
            },
            timeout: 30000
        });
    }

    /**
     * Analyze news article sentiment and extract key insights
     */
    async analyzeArticle(article) {
        try {
            const prompt = `
You are a professional crypto market analyst specializing in PulseChain and DeFi. Analyze this news article and provide:

1. SENTIMENT SCORE (0-100): Overall market sentiment
2. CREDIBILITY SCORE (0-10): Source reliability assessment  
3. TRADING SIGNAL: BUY/SELL/HOLD with confidence %
4. KEY INSIGHTS: 3 bullet points of actionable information
5. MANIPULATION CHECK: Any signs of market manipulation or bias
6. PRICE IMPACT: Potential short-term (24h) and long-term (7d) price effects

Article Title: ${article.title}
Article Content: ${article.content}
Source: ${article.source}
Published: ${article.publishedAt}

Respond in JSON format:
{
    "sentiment": {
        "score": number,
        "label": "positive/neutral/negative",
        "confidence": number
    },
    "credibility": {
        "score": number,
        "reasoning": "string"
    },
    "tradingSignal": {
        "action": "BUY/SELL/HOLD",
        "confidence": number,
        "timeframe": "short/medium/long",
        "reasoning": "string"
    },
    "insights": ["string", "string", "string"],
    "manipulationFlags": {
        "detected": boolean,
        "indicators": ["string"],
        "severity": "low/medium/high"
    },
    "priceImpact": {
        "shortTerm": "positive/negative/neutral",
        "longTerm": "positive/negative/neutral",
        "magnitude": "low/medium/high"
    }
}
`;

            const response = await this.client.post('/v1/chat/completions', {
                model: 'deepseek-chat',
                messages: [
                    {
                        role: 'system',
                        content: 'You are an expert crypto analyst specializing in PulseChain. Provide accurate, unbiased analysis in JSON format only.'
                    },
                    {
                        role: 'user',
                        content: prompt
                    }
                ],
                max_tokens: 1000,
                temperature: 0.3
            });

            let responseContent = response.data.choices[0].message.content;
            
            // Clean up the response - remove markdown code blocks if present
            responseContent = responseContent.replace(/```json\n?/g, '').replace(/```\n?/g, '').trim();
            
            const analysis = JSON.parse(responseContent);
            return {
                success: true,
                analysis,
                usage: response.data.usage
            };

        } catch (error) {
            console.error('DeepSeek analysis error:', error.response?.data || error.message);
            return {
                success: false,
                error: error.message,
                fallback: this.getFallbackAnalysis()
            };
        }
    }

    /**
     * Generate enhanced article summary with noise removal
     */
    async enhanceArticle(article) {
        try {
            const prompt = `
As a professional crypto journalist, rewrite this PulseChain news article to:
1. Remove marketing fluff and bias
2. Focus on factual information and data
3. Highlight key technical developments
4. Provide clear, actionable insights
5. Maintain professional tone

Original Article: ${article.content}

Provide enhanced version that is:
- 2-3 paragraphs maximum
- Fact-focused and unbiased  
- Includes specific numbers/metrics
- Removes promotional language
- Maintains key technical details

Return JSON:
{
    "enhancedContent": "string",
    "keyMetrics": ["string"],
    "technicalHighlights": ["string"],
    "removedBias": ["string"]
}
`;

            const response = await this.client.post('/v1/chat/completions', {
                model: 'deepseek-chat',
                messages: [
                    {
                        role: 'system',
                        content: 'You are a professional crypto journalist focused on factual, unbiased reporting.'
                    },
                    {
                        role: 'user',
                        content: prompt
                    }
                ],
                max_tokens: 800,
                temperature: 0.2
            });

            let responseContent = response.data.choices[0].message.content;
            responseContent = responseContent.replace(/```json\n?/g, '').replace(/```\n?/g, '').trim();
            const enhancement = JSON.parse(responseContent);
            return {
                success: true,
                enhancement,
                usage: response.data.usage
            };

        } catch (error) {
            console.error('Article enhancement error:', error.response?.data || error.message);
            return {
                success: false,
                error: error.message,
                fallback: article.content
            };
        }
    }

    /**
     * Analyze DEX data and generate trading opportunities
     */
    async analyzeDexData(dexData, newsContext = []) {
        try {
            const prompt = `
Analyze this PulseChain DEX data combined with recent news to identify trading opportunities:

DEX Data:
- Price: $${dexData.price}
- Volume 24h: $${dexData.volume24h}
- Liquidity: $${dexData.liquidity}
- Price Change 24h: ${dexData.priceChange24h}%
- Transaction Count: ${dexData.txCount}
- Top Pairs: ${JSON.stringify(dexData.topPairs)}

Recent News Context:
${newsContext.map(n => `- ${n.title} (Sentiment: ${n.sentiment})`).join('\n')}

Provide trading analysis in JSON:
{
    "opportunities": [
        {
            "token": "string",
            "action": "BUY/SELL",
            "confidence": number,
            "reasoning": "string",
            "entry": number,
            "target": number,
            "stopLoss": number,
            "timeframe": "string"
        }
    ],
    "marketOverview": {
        "trend": "bullish/bearish/neutral",
        "strength": number,
        "volume_analysis": "string",
        "liquidity_health": "string"
    },
    "risks": ["string"],
    "catalysts": ["string"]
}
`;

            const response = await this.client.post('/v1/chat/completions', {
                model: 'deepseek-chat',
                messages: [
                    {
                        role: 'system',
                        content: 'You are a DeFi trading expert with deep knowledge of DEX mechanics and market analysis.'
                    },
                    {
                        role: 'user',
                        content: prompt
                    }
                ],
                max_tokens: 1200,
                temperature: 0.4
            });

            let responseContent = response.data.choices[0].message.content;
            
            // Clean up the response - remove markdown code blocks if present
            responseContent = responseContent.replace(/```json\n?/g, '').replace(/```\n?/g, '').trim();
            
            const analysis = JSON.parse(responseContent);
            return {
                success: true,
                analysis,
                usage: response.data.usage
            };

        } catch (error) {
            console.error('DEX analysis error:', error.response?.data || error.message);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Batch analyze multiple articles for correlation patterns
     */
    async analyzeCorrelation(articles, priceData) {
        try {
            const articleSummaries = articles.map(a => ({
                title: a.title,
                sentiment: a.sentiment,
                publishedAt: a.publishedAt,
                source: a.source
            }));

            const prompt = `
Analyze correlation between news events and price movements for PulseChain:

Articles (last 24h): ${JSON.stringify(articleSummaries)}

Price Data: ${JSON.stringify(priceData)}

Identify:
1. News-to-price correlation patterns
2. Source reliability vs price accuracy
3. Sentiment vs actual price movement
4. Time delays between news and market reaction
5. Most influential news types

JSON Response:
{
    "correlation": {
        "strength": number,
        "direction": "positive/negative/mixed",
        "lag_time": "minutes/hours",
        "confidence": number
    },
    "patterns": [
        {
            "news_type": "string",
            "price_impact": "string",
            "reliability": number
        }
    ],
    "predictions": {
        "next_24h": "bullish/bearish/neutral",
        "confidence": number,
        "key_factors": ["string"]
    }
}
`;

            const response = await this.client.post('/v1/chat/completions', {
                model: 'deepseek-chat',
                messages: [
                    {
                        role: 'system',
                        content: 'You are a quantitative analyst specializing in news-price correlation in crypto markets.'
                    },
                    {
                        role: 'user',
                        content: prompt
                    }
                ],
                max_tokens: 1000,
                temperature: 0.3
            });

            let responseContent = response.data.choices[0].message.content;
            responseContent = responseContent.replace(/```json\n?/g, '').replace(/```\n?/g, '').trim();
            const correlation = JSON.parse(responseContent);
            return {
                success: true,
                correlation,
                usage: response.data.usage
            };

        } catch (error) {
            console.error('Correlation analysis error:', error.response?.data || error.message);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Test API connection and validate key
     */
    async testConnection() {
        try {
            const response = await this.client.post('/v1/chat/completions', {
                model: 'deepseek-chat',
                messages: [
                    {
                        role: 'user',
                        content: 'Hello, this is a connection test. Please respond with "Connection successful".'
                    }
                ],
                max_tokens: 50
            });

            return {
                success: true,
                message: 'DeepSeek API connection successful',
                response: response.data.choices[0].message.content,
                usage: response.data.usage
            };
        } catch (error) {
            return {
                success: false,
                message: 'DeepSeek API connection failed',
                error: error.response?.data || error.message
            };
        }
    }

    /**
     * Fallback analysis when API fails
     */
    getFallbackAnalysis() {
        return {
            sentiment: {
                score: 50,
                label: 'neutral',
                confidence: 0
            },
            credibility: {
                score: 5,
                reasoning: 'Unable to analyze - API error'
            },
            tradingSignal: {
                action: 'HOLD',
                confidence: 0,
                timeframe: 'medium',
                reasoning: 'Insufficient analysis data'
            },
            insights: ['API analysis unavailable', 'Manual review required', 'Check connection'],
            manipulationFlags: {
                detected: false,
                indicators: [],
                severity: 'low'
            },
            priceImpact: {
                shortTerm: 'neutral',
                longTerm: 'neutral',
                magnitude: 'low'
            }
        };
    }
}

module.exports = new DeepSeekService();