/**
 * Database Service
 * Better SQLite3 database management for PulseChain News Hub
 */

const Database = require('better-sqlite3');
const fs = require('fs');
const path = require('path');

class DatabaseService {
    constructor() {
        this.dbPath = process.env.DB_PATH || './data/pulsechain_news.db';
        this.db = null;
        this.isConnected = false;
        
        // Ensure data directory exists
        const dataDir = path.dirname(this.dbPath);
        if (!fs.existsSync(dataDir)) {
            fs.mkdirSync(dataDir, { recursive: true });
        }
    }

    /**
     * Initialize database connection and create tables
     */
    async initialize() {
        try {
            console.log('Initializing database...');
            
            this.db = new Database(this.dbPath);
            
            // Enable WAL mode for better performance
            this.db.pragma('journal_mode = WAL');
            this.db.pragma('foreign_keys = ON');
            
            console.log('Connected to SQLite database');
            
            // Create tables from schema
            await this.createTables();
            
            this.isConnected = true;
            console.log('Database initialized successfully');
            
        } catch (error) {
            console.error('Database initialization error:', error);
            throw error;
        }
    }

    /**
     * Create database tables from schema file
     */
    async createTables() {
        try {
            const schemaPath = path.join(__dirname, 'schema.sql');
            const schema = fs.readFileSync(schemaPath, 'utf8');
            
            // Split and execute each SQL statement
            const statements = schema.split(';').filter(stmt => stmt.trim());
            
            for (const statement of statements) {
                if (statement.trim()) {
                    this.db.exec(statement.trim());
                }
            }
            
            console.log('Database schema created successfully');
            
        } catch (error) {
            console.error('Schema creation error:', error);
            throw error;
        }
    }

    /**
     * Execute SQL query with parameters
     */
    run(sql, params = []) {
        try {
            const stmt = this.db.prepare(sql);
            const result = stmt.run(...params);
            return { id: result.lastInsertRowid, changes: result.changes };
        } catch (error) {
            console.error('SQL Error:', error.message);
            console.error('SQL:', sql);
            throw error;
        }
    }

    /**
     * Execute SELECT query
     */
    get(sql, params = []) {
        try {
            const stmt = this.db.prepare(sql);
            return stmt.get(...params);
        } catch (error) {
            console.error('SQL Error:', error.message);
            throw error;
        }
    }

    /**
     * Execute SELECT query returning all rows
     */
    all(sql, params = []) {
        try {
            const stmt = this.db.prepare(sql);
            return stmt.all(...params);
        } catch (error) {
            console.error('SQL Error:', error.message);
            throw error;
        }
    }

    // ========== NEWS SOURCES OPERATIONS ==========

    /**
     * Get all active news sources
     */
    async getSources() {
        return this.all(`
            SELECT * FROM sources 
            WHERE is_active = 1 
            ORDER BY credibility_score DESC
        `);
    }

    /**
     * Update source statistics
     */
    async updateSourceStats(sourceId, totalArticles, successRate) {
        return this.run(`
            UPDATE sources 
            SET total_articles = ?, success_rate = ?, last_scraped_at = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        `, [totalArticles, successRate, sourceId]);
    }

    // ========== ARTICLES OPERATIONS ==========

    /**
     * Insert new article
     */
    async insertArticle(article) {
        const sql = `
            INSERT INTO articles (
                source_id, title, content, description, url, published_at, 
                author, image_url, guid, categories, scraped_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        `;
        
        // Sanitize data - convert undefined to null
        const sanitizeValue = (value) => {
            if (value === undefined) return null;
            if (value === null) return null;
            if (typeof value === 'string' && value.trim() === '') return null;
            if (typeof value === 'object') return JSON.stringify(value);
            if (value instanceof Date) return value.toISOString();
            return value;
        };

        const sanitizedCategories = Array.isArray(article.categories) 
            ? JSON.stringify(article.categories) 
            : JSON.stringify([]);

        return this.run(sql, [
            sanitizeValue(article.sourceId),
            sanitizeValue(article.title) || 'Untitled',
            sanitizeValue(article.content),
            sanitizeValue(article.description),
            sanitizeValue(article.url),
            sanitizeValue(article.publishedAt),
            sanitizeValue(article.author),
            sanitizeValue(article.imageUrl),
            sanitizeValue(article.guid),
            sanitizedCategories
        ]);
    }

    /**
     * Update article with AI analysis results
     */
    async updateArticleAnalysis(articleId, analysis) {
        const sql = `
            UPDATE articles SET
                sentiment_score = ?,
                sentiment_label = ?,
                sentiment_confidence = ?,
                credibility_score = ?,
                trading_signal = ?,
                signal_confidence = ?,
                signal_timeframe = ?,
                price_impact_short = ?,
                price_impact_long = ?,
                enhanced_content = ?,
                key_insights = ?,
                manipulation_flags = ?,
                is_processed = 1,
                processed_at = CURRENT_TIMESTAMP
            WHERE id = ?
        `;

        return this.run(sql, [
            analysis.sentiment?.score || null,
            analysis.sentiment?.label || null,
            analysis.sentiment?.confidence || null,
            analysis.credibility?.score || null,
            analysis.tradingSignal?.action || null,
            analysis.tradingSignal?.confidence || null,
            analysis.tradingSignal?.timeframe || null,
            analysis.priceImpact?.shortTerm || null,
            analysis.priceImpact?.longTerm || null,
            analysis.enhancedContent || null,
            JSON.stringify(analysis.insights || []),
            JSON.stringify(analysis.manipulationFlags || {}),
            articleId
        ]);
    }

    /**
     * Get recent articles with pagination
     */
    async getRecentArticles(limit = 50, offset = 0, filters = {}) {
        let sql = `
            SELECT a.*, s.name as source_name, s.credibility_score as source_credibility
            FROM articles a
            JOIN sources s ON a.source_id = s.id
            WHERE 1=1
        `;
        const params = [];

        // Apply filters
        if (filters.sentiment) {
            sql += ` AND a.sentiment_label = ?`;
            params.push(filters.sentiment);
        }

        if (filters.minCredibility) {
            sql += ` AND s.credibility_score >= ?`;
            params.push(filters.minCredibility);
        }

        if (filters.timeframe) {
            const hours = filters.timeframe === '24h' ? 24 : filters.timeframe === '7d' ? 168 : 24;
            sql += ` AND a.published_at > datetime(current_timestamp, '-${hours} hours')`;
        }

        sql += ` ORDER BY a.published_at DESC LIMIT ? OFFSET ?`;
        params.push(limit, offset);

        return this.all(sql, params);
    }

    /**
     * Get articles by sentiment
     */
    async getArticlesBySentiment(sentiment) {
        return this.all(`
            SELECT a.*, s.name as source_name 
            FROM articles a
            JOIN sources s ON a.source_id = s.id
            WHERE a.sentiment_label = ?
            ORDER BY a.published_at DESC
            LIMIT 100
        `, [sentiment]);
    }

    /**
     * Check if article URL already exists
     */
    async articleExists(url) {
        const result = this.get('SELECT id FROM articles WHERE url = ?', [url]);
        return !!result;
    }

    // ========== DEX DATA OPERATIONS ==========

    /**
     * Insert DEX data
     */
    async insertDexData(dexData) {
        const sql = `
            INSERT INTO dex_data (
                token_symbol, token_name, token_address, price_usd, price_change_24h,
                volume_24h, market_cap, liquidity, health_score, health_grade,
                volatility, trading_pairs_count, data_source, timestamp
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        `;

        const promises = [];
        
        for (const [symbol, data] of Object.entries(dexData.prices || {})) {
            promises.push(this.run(sql, [
                symbol,
                data.name || symbol,
                data.address || null,
                data.price || 0,
                data.change24h || 0,
                data.volume24h || 0,
                data.marketCap || 0,
                data.liquidity || 0,
                dexData.health?.score || null,
                dexData.health?.grade || null,
                data.volatility || null,
                data.tradingPairs || null,
                'api_aggregate'
            ]));
        }

        return Promise.all(promises);
    }

    /**
     * Get latest DEX data
     */
    async getLatestDexData() {
        return this.all(`
            SELECT * FROM market_overview
            ORDER BY volume_24h DESC
        `);
    }

    /**
     * Get price history for token
     */
    async getPriceHistory(tokenSymbol, hours = 24) {
        return this.all(`
            SELECT * FROM price_history 
            WHERE token_symbol = ? 
              AND timestamp > datetime(current_timestamp, '-${hours} hours')
            ORDER BY timestamp ASC
        `, [tokenSymbol]);
    }

    // ========== TRADING SIGNALS OPERATIONS ==========

    /**
     * Insert trading signal
     */
    async insertTradingSignal(signal) {
        const sql = `
            INSERT INTO trading_signals (
                article_id, token_symbol, signal_type, confidence, timeframe,
                reasoning, entry_price, target_price, stop_loss, risk_level,
                market_sentiment, volume_analysis, news_catalyst, expires_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `;

        return this.run(sql, [
            signal.articleId,
            signal.tokenSymbol,
            signal.signalType,
            signal.confidence,
            signal.timeframe,
            signal.reasoning,
            signal.entryPrice || null,
            signal.targetPrice || null,
            signal.stopLoss || null,
            signal.riskLevel || 'medium',
            signal.marketSentiment || null,
            signal.volumeAnalysis || null,
            signal.newsCatalyst || null,
            signal.expiresAt || null
        ]);
    }

    /**
     * Get active trading signals
     */
    async getActiveTradingSignals() {
        return this.all(`
            SELECT * FROM active_signals
            ORDER BY confidence DESC, created_at DESC
            LIMIT 50
        `);
    }

    /**
     * Update signal performance
     */
    async updateSignalPerformance(signalId, outcome, performanceScore) {
        return this.run(`
            UPDATE trading_signals 
            SET actual_outcome = ?, performance_score = ?, is_active = 0
            WHERE id = ?
        `, [outcome, performanceScore, signalId]);
    }

    // ========== ANALYTICS OPERATIONS ==========

    /**
     * Insert analytics metric
     */
    async insertAnalytic(metricName, metricValue, metricType, category, timePeriod = 'daily') {
        return this.run(`
            INSERT INTO analytics (metric_name, metric_value, metric_type, category, time_period)
            VALUES (?, ?, ?, ?, ?)
        `, [metricName, metricValue, metricType, category, timePeriod]);
    }

    /**
     * Get analytics for dashboard
     */
    async getAnalytics(category = null, hours = 24) {
        let sql = `
            SELECT * FROM analytics 
            WHERE timestamp > datetime(current_timestamp, '-${hours} hours')
        `;
        const params = [];

        if (category) {
            sql += ` AND category = ?`;
            params.push(category);
        }

        sql += ` ORDER BY timestamp DESC`;

        return this.all(sql, params);
    }

    /**
     * Get database statistics
     */
    async getStats() {
        const stats = {};

        try {
            const articlesCount = this.get('SELECT COUNT(*) as count FROM articles');
            stats.totalArticles = articlesCount.count;

            const processedCount = this.get('SELECT COUNT(*) as count FROM articles WHERE is_processed = 1');
            stats.processedArticles = processedCount.count;

            const signalsCount = this.get('SELECT COUNT(*) as count FROM trading_signals WHERE is_active = 1');
            stats.activeSignals = signalsCount.count;

            const sourcesCount = this.get('SELECT COUNT(*) as count FROM sources WHERE is_active = 1');
            stats.activeSources = sourcesCount.count;

            const recentArticles = this.get(`
                SELECT COUNT(*) as count FROM articles 
                WHERE published_at > datetime(current_timestamp, '-24 hours')
            `);
            stats.articlesLast24h = recentArticles.count;

            stats.lastUpdated = new Date().toISOString();

        } catch (error) {
            console.error('Stats query error:', error);
        }

        return stats;
    }

    /**
     * Clean old data
     */
    async cleanup(daysOld = 30) {
        const cutoffDate = `datetime(current_timestamp, '-${daysOld} days')`;
        
        const results = {};
        
        // Clean old price history
        const priceCleanup = this.run(`
            DELETE FROM price_history WHERE timestamp < ${cutoffDate}
        `);
        results.oldPriceData = priceCleanup.changes;

        // Clean old analytics
        const analyticsCleanup = this.run(`
            DELETE FROM analytics WHERE timestamp < ${cutoffDate}
        `);
        results.oldAnalytics = analyticsCleanup.changes;

        // Clean expired signals
        const signalsCleanup = this.run(`
            UPDATE trading_signals SET is_active = 0 
            WHERE expires_at IS NOT NULL AND expires_at < current_timestamp
        `);
        results.expiredSignals = signalsCleanup.changes;

        console.log('Database cleanup completed:', results);
        return results;
    }

    /**
     * Close database connection
     */
    async close() {
        try {
            if (this.db) {
                this.db.close();
                console.log('Database connection closed');
                this.isConnected = false;
            }
        } catch (error) {
            console.error('Database close error:', error);
        }
    }

    /**
     * Test database connection
     */
    async testConnection() {
        try {
            const result = this.get('SELECT current_timestamp as current_time');
            return {
                success: true,
                message: 'Database connection successful',
                currentTime: result.current_time,
                isConnected: this.isConnected
            };
        } catch (error) {
            return {
                success: false,
                message: 'Database connection failed',
                error: error.message
            };
        }
    }
}

module.exports = new DatabaseService();