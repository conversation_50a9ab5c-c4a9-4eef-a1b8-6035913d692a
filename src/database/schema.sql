-- PulseChain News Hub Database Schema
-- SQLite database for local development

-- News Sources Table
CREATE TABLE IF NOT EXISTS sources (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL UNIQUE,
    url TEXT NOT NULL,
    type TEXT NOT NULL, -- 'rss', 'web', 'social'
    credibility_score REAL DEFAULT 5.0,
    is_active BOOLEAN DEFAULT 1,
    tags TEXT, -- JSON array of tags
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    last_scraped_at DATETIME,
    total_articles INTEGER DEFAULT 0,
    success_rate REAL DEFAULT 0.0
);

-- Articles Table
CREATE TABLE IF NOT EXISTS articles (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    source_id INTEGER NOT NULL,
    title TEXT NOT NULL,
    content TEXT,
    description TEXT,
    url TEXT NOT NULL UNIQUE,
    published_at DATETIME NOT NULL,
    scraped_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    author TEXT,
    image_url TEXT,
    guid TEXT,
    categories TEXT, -- JSON array
    
    -- AI Analysis Results
    sentiment_score REAL,
    sentiment_label TEXT, -- 'positive', 'negative', 'neutral'
    sentiment_confidence REAL,
    credibility_score REAL,
    
    -- Trading Analysis
    trading_signal TEXT, -- 'BUY', 'SELL', 'HOLD'
    signal_confidence REAL,
    signal_timeframe TEXT, -- 'short', 'medium', 'long'
    price_impact_short TEXT, -- 'positive', 'negative', 'neutral'
    price_impact_long TEXT,
    
    -- Content Enhancement
    enhanced_content TEXT,
    key_insights TEXT, -- JSON array
    manipulation_flags TEXT, -- JSON object
    
    -- Processing Status
    is_processed BOOLEAN DEFAULT 0,
    processing_error TEXT,
    processed_at DATETIME,
    
    FOREIGN KEY (source_id) REFERENCES sources (id)
);

-- DEX Data Table
CREATE TABLE IF NOT EXISTS dex_data (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    token_symbol TEXT NOT NULL,
    token_name TEXT,
    token_address TEXT,
    price_usd REAL NOT NULL,
    price_change_24h REAL,
    volume_24h REAL,
    market_cap REAL,
    liquidity REAL,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    -- Market Health Metrics
    health_score REAL,
    health_grade TEXT,
    volatility REAL,
    trading_pairs_count INTEGER,
    
    -- Data Source
    data_source TEXT DEFAULT 'multiple',
    is_verified BOOLEAN DEFAULT 0
);

-- Trading Signals Table
CREATE TABLE IF NOT EXISTS trading_signals (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    article_id INTEGER,
    token_symbol TEXT NOT NULL,
    signal_type TEXT NOT NULL, -- 'BUY', 'SELL', 'HOLD'
    confidence REAL NOT NULL,
    timeframe TEXT NOT NULL,
    
    -- Signal Details
    reasoning TEXT,
    entry_price REAL,
    target_price REAL,
    stop_loss REAL,
    risk_level TEXT, -- 'low', 'medium', 'high'
    
    -- Market Context
    market_sentiment TEXT,
    volume_analysis TEXT,
    news_catalyst TEXT,
    
    -- Performance Tracking
    is_active BOOLEAN DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    expires_at DATETIME,
    actual_outcome TEXT, -- 'success', 'failure', 'partial'
    performance_score REAL,
    
    FOREIGN KEY (article_id) REFERENCES articles (id)
);

-- Price History Table
CREATE TABLE IF NOT EXISTS price_history (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    token_symbol TEXT NOT NULL,
    price_usd REAL NOT NULL,
    volume_24h REAL,
    market_cap REAL,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    -- OHLC Data
    open_price REAL,
    high_price REAL,
    low_price REAL,
    close_price REAL,
    
    -- Technical Indicators
    rsi REAL,
    moving_avg_20 REAL,
    moving_avg_50 REAL
);

-- News-Price Correlation Table
CREATE TABLE IF NOT EXISTS news_correlations (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    article_id INTEGER NOT NULL,
    token_symbol TEXT NOT NULL,
    
    -- Correlation Metrics
    correlation_strength REAL, -- -1.0 to 1.0
    time_lag_minutes INTEGER, -- How long after news did price react
    price_impact_percent REAL,
    volume_impact_percent REAL,
    
    -- Analysis Window
    analysis_window_hours INTEGER DEFAULT 24,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (article_id) REFERENCES articles (id)
);

-- User Preferences/Settings Table (for future use)
CREATE TABLE IF NOT EXISTS user_settings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id TEXT DEFAULT 'default',
    
    -- Alert Settings
    price_alerts_enabled BOOLEAN DEFAULT 1,
    news_alerts_enabled BOOLEAN DEFAULT 1,
    signal_alerts_enabled BOOLEAN DEFAULT 1,
    
    -- Filtering Preferences
    min_credibility_score REAL DEFAULT 5.0,
    preferred_sources TEXT, -- JSON array
    excluded_sources TEXT, -- JSON array
    favorite_tokens TEXT, -- JSON array
    
    -- Display Preferences
    timezone TEXT DEFAULT 'UTC',
    date_format TEXT DEFAULT 'ISO',
    theme TEXT DEFAULT 'light',
    
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Analytics/Stats Table
CREATE TABLE IF NOT EXISTS analytics (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    metric_name TEXT NOT NULL,
    metric_value REAL NOT NULL,
    metric_type TEXT NOT NULL, -- 'count', 'rate', 'score', 'percentage'
    category TEXT, -- 'scraping', 'analysis', 'trading', 'performance'
    
    -- Time-based metrics
    time_period TEXT, -- 'hourly', 'daily', 'weekly'
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    -- Additional context
    metadata TEXT -- JSON object for extra data
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_articles_published_at ON articles (published_at);
CREATE INDEX IF NOT EXISTS idx_articles_source_id ON articles (source_id);
CREATE INDEX IF NOT EXISTS idx_articles_sentiment ON articles (sentiment_label, sentiment_score);
CREATE INDEX IF NOT EXISTS idx_articles_processed ON articles (is_processed, processed_at);

CREATE INDEX IF NOT EXISTS idx_dex_data_symbol_timestamp ON dex_data (token_symbol, timestamp);
CREATE INDEX IF NOT EXISTS idx_trading_signals_active ON trading_signals (is_active, created_at);
CREATE INDEX IF NOT EXISTS idx_trading_signals_token ON trading_signals (token_symbol, signal_type);
CREATE INDEX IF NOT EXISTS idx_price_history_token_timestamp ON price_history (token_symbol, timestamp);
CREATE INDEX IF NOT EXISTS idx_analytics_metric_timestamp ON analytics (metric_name, timestamp);

-- Insert default sources
INSERT OR IGNORE INTO sources (name, url, type, credibility_score, tags) VALUES
('CoinTelegraph', 'https://cointelegraph.com/rss', 'rss', 8.5, '["general", "analysis"]'),
('CryptoNews', 'https://cryptonews.com/news/feed/', 'rss', 7.8, '["general", "breaking"]'),
('Decrypt', 'https://decrypt.co/feed', 'rss', 8.2, '["defi", "technology"]'),
('CoinDesk', 'https://www.coindesk.com/arc/outboundfeeds/rss/', 'rss', 9.0, '["institutional", "analysis"]'),
('BeInCrypto', 'https://beincrypto.com/feed/', 'rss', 7.5, '["altcoins", "defi"]'),
('CryptoSlate', 'https://cryptoslate.com/feed/', 'rss', 8.0, '["analysis", "projects"]'),
('AMBCrypto', 'https://ambcrypto.com/feed/', 'rss', 7.2, '["analysis", "market"]'),
('NewsBTC', 'https://www.newsbtc.com/feed/', 'rss', 7.8, '["technical", "trading"]'),
('Reddit PulseChain', 'https://www.reddit.com/r/PulseChain/.rss', 'social', 6.5, '["community", "social"]');

-- Insert default user settings
INSERT OR IGNORE INTO user_settings (user_id) VALUES ('default');

-- Create views for common queries
CREATE VIEW IF NOT EXISTS recent_articles AS
SELECT 
    a.*,
    s.name as source_name,
    s.credibility_score as source_credibility
FROM articles a
JOIN sources s ON a.source_id = s.id
WHERE a.published_at > datetime('now', '-7 days')
ORDER BY a.published_at DESC;

CREATE VIEW IF NOT EXISTS active_signals AS
SELECT 
    ts.*,
    a.title as article_title,
    a.published_at as news_date
FROM trading_signals ts
LEFT JOIN articles a ON ts.article_id = a.id
WHERE ts.is_active = 1 
  AND (ts.expires_at IS NULL OR ts.expires_at > current_timestamp)
ORDER BY ts.created_at DESC;

CREATE VIEW IF NOT EXISTS market_overview AS
SELECT 
    token_symbol,
    price_usd,
    price_change_24h,
    volume_24h,
    health_score,
    health_grade,
    timestamp
FROM dex_data d1
WHERE d1.timestamp = (
    SELECT MAX(d2.timestamp) 
    FROM dex_data d2 
    WHERE d2.token_symbol = d1.token_symbol
)
ORDER BY volume_24h DESC;