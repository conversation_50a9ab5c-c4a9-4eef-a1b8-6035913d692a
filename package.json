{"name": "pulsechain-news-hub", "version": "1.0.0", "description": "AI-powered PulseChain news aggregator with trading signals", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "build:css": "npx @dhiwise/component-tagger && npx tailwindcss -i ./css/tailwind.css -o ./css/main.css", "watch:css": "npx @dhiwise/component-tagger && npx tailwindcss -i ./css/tailwind.css -o ./css/main.css --watch", "test": "node test-connections.js"}, "dependencies": {"@dhiwise/component-tagger": "^1.0.10", "@tailwindcss/forms": "^0.5.7", "tailwindcss-animate": "^1.0.7", "tailwindcss-elevation": "^2.0.0", "tailwindcss-fluid-type": "^2.0.7", "express": "^4.18.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "axios": "^1.6.0", "cheerio": "^1.0.0-rc.12", "node-cron": "^3.0.3", "better-sqlite3": "^9.2.2", "ws": "^8.14.2", "rss-parser": "^3.13.0", "express-rate-limit": "^7.1.5"}, "devDependencies": {"tailwindcss": "^3.4.17", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/container-queries": "^0.1.1", "@tailwindcss/line-clamp": "^0.1.0", "@tailwindcss/typography": "^0.5.16", "nodemon": "^3.0.1"}, "engines": {"node": ">=18.0.0"}, "author": "PulseChain News Hub", "license": "MIT", "keywords": ["pulsechain", "crypto", "news", "ai", "trading", "defi", "deepseek"]}