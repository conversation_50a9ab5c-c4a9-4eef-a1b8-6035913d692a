/**
 * Connection Test Script
 * Tests all integrations and connections
 */

require('dotenv').config();

const database = require('./src/database/database');
const deepseek = require('./src/services/deepseek');
const scraper = require('./src/services/scraper');
const dexService = require('./src/services/dex');
const analyzer = require('./src/services/analyzer');

console.log('🧪 PulseChain News Hub - Connection Tests\n');

async function testDatabase() {
    console.log('📊 Testing Database Connection...');
    try {
        await database.initialize();
        const result = await database.testConnection();
        
        if (result.success) {
            console.log('✅ Database: Connected successfully');
            
            // Test basic operations
            const stats = await database.getStats();
            console.log(`   - Total articles: ${stats.totalArticles || 0}`);
            console.log(`   - Processed articles: ${stats.processedArticles || 0}`);
            console.log(`   - Active signals: ${stats.activeSignals || 0}`);
            
        } else {
            console.log('❌ Database: Connection failed');
            console.log(`   Error: ${result.error}`);
        }
        
        return result.success;
    } catch (error) {
        console.log('❌ Database: Initialization failed');
        console.log(`   Error: ${error.message}`);
        return false;
    }
}

async function testDeepSeek() {
    console.log('\n🤖 Testing DeepSeek AI Connection...');
    try {
        const result = await deepseek.testConnection();
        
        if (result.success) {
            console.log('✅ DeepSeek: API connected successfully');
            console.log(`   Response: ${result.response}`);
            console.log(`   Usage: ${JSON.stringify(result.usage)}`);
            
            // Test analysis function
            console.log('   Testing article analysis...');
            const testArticle = {
                title: 'PulseChain Network Sees 25% Price Increase After Major Update',
                content: 'The PulseChain blockchain network has experienced significant growth following the implementation of new scaling features. Trading volume increased by 40% in the past 24 hours.',
                source: 'Test Source',
                publishedAt: new Date().toISOString()
            };
            
            const analysis = await deepseek.analyzeArticle(testArticle);
            if (analysis.success) {
                console.log('✅ DeepSeek: Article analysis working');
                console.log(`   Sentiment: ${analysis.analysis.sentiment.label} (${analysis.analysis.sentiment.score})`);
                console.log(`   Signal: ${analysis.analysis.tradingSignal.action} (${analysis.analysis.tradingSignal.confidence})`);
            } else {
                console.log('⚠️  DeepSeek: Analysis failed, using fallback');
            }
            
        } else {
            console.log('❌ DeepSeek: API connection failed');
            console.log(`   Error: ${result.error}`);
        }
        
        return result.success;
    } catch (error) {
        console.log('❌ DeepSeek: Test failed');
        console.log(`   Error: ${error.message}`);
        return false;
    }
}

async function testNewsScraping() {
    console.log('\n📰 Testing News Scraping...');
    try {
        console.log('   Getting scraping statistics...');
        const stats = scraper.getScrapingStats();
        console.log(`✅ News Scraper: ${stats.sources} sources configured`);
        console.log(`   Keywords: ${stats.keywords} PulseChain keywords`);
        console.log(`   Average credibility: ${stats.averageCredibility.toFixed(1)}`);
        
        // Test scraping a single source
        console.log('   Testing RSS parsing...');
        try {
            const testSources = [
                {
                    name: 'CoinTelegraph',
                    url: 'https://cointelegraph.com/rss',
                    type: 'rss',
                    credibility: 8.5,
                    tags: ['general', 'analysis']
                }
            ];
            
            const testSource = testSources[0];
            const articles = await scraper.scrapeSource(testSource);
            
            console.log(`✅ RSS Parsing: Retrieved ${articles.length} articles from ${testSource.name}`);
            
            if (articles.length > 0) {
                const pulseChainRelated = articles.filter(a => scraper.isPulseChainRelated(a));
                console.log(`   PulseChain related: ${pulseChainRelated.length} articles`);
            }
            
        } catch (scrapeError) {
            console.log('⚠️  RSS Parsing: Limited test due to network or rate limiting');
            console.log(`   Note: ${scrapeError.message.substring(0, 100)}...`);
        }
        
        return true;
    } catch (error) {
        console.log('❌ News Scraping: Test failed');
        console.log(`   Error: ${error.message}`);
        return false;
    }
}

async function testDexIntegration() {
    console.log('\n💹 Testing DEX Data Integration...');
    try {
        console.log('   Getting service statistics...');
        const stats = dexService.getServiceStats();
        console.log(`✅ DEX Service: ${stats.supportedTokens.length} tokens supported`);
        console.log(`   Tokens: ${stats.supportedTokens.join(', ')}`);
        console.log(`   Cache size: ${stats.cacheSize} entries`);
        
        console.log('   Testing price data fetch...');
        const priceData = await dexService.getPriceData();
        console.log('✅ Price Data: Retrieved successfully');
        
        Object.entries(priceData).forEach(([symbol, data]) => {
            console.log(`   ${symbol}: $${data.price} (${data.change24h > 0 ? '+' : ''}${data.change24h.toFixed(2)}%)`);
        });
        
        console.log('   Testing market health calculation...');
        const dexData = await dexService.getDexData();
        if (dexData.health) {
            console.log(`✅ Market Health: ${dexData.health.grade} (${dexData.health.score}/100)`);
            console.log(`   Factors: ${dexData.health.factors.join(', ')}`);
        }
        
        return true;
    } catch (error) {
        console.log('❌ DEX Integration: Test failed');
        console.log(`   Error: ${error.message}`);
        return false;
    }
}

async function testAnalysisPipeline() {
    console.log('\n🔍 Testing Analysis Pipeline...');
    try {
        const status = analyzer.getStatus();
        console.log('✅ Analysis Pipeline: Service initialized');
        console.log(`   Running: ${status.isRunning ? 'Yes' : 'No'}`);
        console.log(`   Queue: ${status.queueLength} items`);
        console.log(`   Total processed: ${status.stats.totalProcessed}`);
        console.log(`   Success rate: ${status.stats.successCount}/${status.stats.successCount + status.stats.errorCount}`);
        
        if (!status.isRunning) {
            console.log('   Note: Full pipeline test skipped to avoid costs');
            console.log('   Use "npm run analysis" to test the complete pipeline');
        }
        
        return true;
    } catch (error) {
        console.log('❌ Analysis Pipeline: Test failed');
        console.log(`   Error: ${error.message}`);
        return false;
    }
}

async function testAPIEndpoints() {
    console.log('\n🌐 Testing API Readiness...');
    
    // Test if we can start a basic server
    try {
        const express = require('express');
        const testApp = express();
        const testPort = 3099;
        
        testApp.get('/test', (req, res) => {
            res.json({ success: true, message: 'API test endpoint working' });
        });
        
        const server = testApp.listen(testPort, () => {
            console.log('✅ Express Server: Can start successfully');
        });
        
        server.close();
        console.log('✅ Express Server: Closed cleanly');
        
        return true;
    } catch (error) {
        console.log('❌ Express Server: Test failed');
        console.log(`   Error: ${error.message}`);
        return false;
    }
}

async function runAllTests() {
    console.log('Starting comprehensive connection tests...\n');
    
    const results = {};
    
    results.database = await testDatabase();
    results.deepseek = await testDeepSeek();
    results.scraping = await testNewsScraping();
    results.dex = await testDexIntegration();
    results.analysis = await testAnalysisPipeline();
    results.api = await testAPIEndpoints();
    
    console.log('\n📋 Test Summary');
    console.log('===============');
    
    let passedTests = 0;
    const totalTests = Object.keys(results).length;
    
    Object.entries(results).forEach(([test, passed]) => {
        const status = passed ? '✅ PASS' : '❌ FAIL';
        console.log(`${test.padEnd(12)}: ${status}`);
        if (passed) passedTests++;
    });
    
    console.log(`\nResult: ${passedTests}/${totalTests} tests passed`);
    
    if (passedTests === totalTests) {
        console.log('🎉 All systems ready! You can start the server with "npm start"');
        console.log('🚀 Your PulseChain News Hub is ready for production use!');
    } else {
        console.log('⚠️  Some tests failed. Check the errors above before starting the server.');
        
        // Provide specific guidance
        if (!results.database) {
            console.log('   - Database: Check file permissions and SQLite installation');
        }
        if (!results.deepseek) {
            console.log('   - DeepSeek: Verify API key and internet connection');
        }
        if (!results.scraping) {
            console.log('   - Scraping: Check internet connection and RSS feed availability');
        }
        if (!results.dex) {
            console.log('   - DEX: Check API endpoints and network connectivity');
        }
    }
    
    console.log('\n📚 Next Steps:');
    console.log('   1. Run "npm install" if you haven\'t already');
    console.log('   2. Run "npm start" to start the full server');
    console.log('   3. Visit http://localhost:3001/dashboard in your browser');
    console.log('   4. Check http://localhost:3001/api/health for API status');
    
    // Close database connection
    await database.close();
    
    process.exit(passedTests === totalTests ? 0 : 1);
}

// Handle cleanup on exit
process.on('SIGINT', async () => {
    console.log('\n\nTest interrupted by user');
    await database.close();
    process.exit(1);
});

// Run tests
runAllTests().catch(error => {
    console.error('\nUnexpected error during testing:', error);
    database.close();
    process.exit(1);
});